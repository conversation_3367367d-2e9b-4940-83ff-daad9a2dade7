#!/usr/bin/env node

/**
 * Supabase RLS策略验证测试脚本
 * 用于验证Row Level Security策略是否按预期工作
 * 
 * 使用方法:
 * node test-rls.js
 * 
 * 或者添加到package.json scripts中:
 * npm run test:rls
 */

const { createClient } = require('@supabase/supabase-js');
const chalk = require('chalk');

// Supabase配置 - 从您的analytics.js中提取
const SUPABASE_URL = 'https://egozkxbkonmawgvngtjq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVnb3preGJrb25tYXdndm5ndGpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUxODI4NTMsImV4cCI6MjA3MDc1ODg1M30.zdDd9TTG4t3QR_VaVKnqxX2lyD2j5P8Uf6wqzT0340U';

class RLSPolicyTester {
    constructor() {
        this.supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        this.testResults = [];
        this.startTime = Date.now();
    }

    // 记录测试结果
    logResult(testName, success, message, details = null) {
        const result = {
            test: testName,
            success,
            message,
            details,
            timestamp: new Date().toISOString()
        };
        this.testResults.push(result);

        const icon = success ? '✅' : '❌';
        const color = success ? chalk.green : chalk.red;
        console.log(`${icon} ${color(testName)}: ${message}`);
        
        if (details) {
            console.log(`   ${chalk.gray('详情:')} ${JSON.stringify(details, null, 2)}`);
        }
    }

    // 测试1: 验证可以读取styles表
    async testReadStyles() {
        try {
            const { data, error } = await this.supabase
                .from('styles')
                .select('id, name, slug')
                .limit(3);

            if (error) {
                this.logResult(
                    '读取styles表',
                    false,
                    `读取失败: ${error.message}`,
                    { code: error.code, hint: error.hint }
                );
                return false;
            }

            this.logResult(
                '读取styles表',
                true,
                `成功读取 ${data.length} 条记录`,
                { count: data.length, sample: data[0] }
            );
            return true;
        } catch (err) {
            this.logResult('读取styles表', false, `异常: ${err.message}`);
            return false;
        }
    }

    // 测试2: 验证可以插入analytics数据
    async testInsertAnalytics() {
        const testData = {
            style_id: null, // 允许null值
            event_type: 'view',
            session_id: `test-${Date.now()}`,
            timestamp: new Date().toISOString(),
            user_agent: 'RLS-Test-Script/1.0',
            ip_hash: 'test-hash-' + Math.random().toString(36).substr(2, 8)
        };

        try {
            const { data, error } = await this.supabase
                .from('analytics')
                .insert([testData])
                .select();

            if (error) {
                this.logResult(
                    '插入analytics数据',
                    false,
                    `插入失败: ${error.message}`,
                    { code: error.code, testData }
                );
                return false;
            }

            this.logResult(
                '插入analytics数据',
                true,
                '成功插入测试数据',
                { insertedId: data[0]?.id, sessionId: testData.session_id }
            );
            return data[0]?.id;
        } catch (err) {
            this.logResult('插入analytics数据', false, `异常: ${err.message}`);
            return false;
        }
    }

    // 测试3: 验证可以读取analytics数据
    async testReadAnalytics() {
        try {
            const { data, error } = await this.supabase
                .from('analytics')
                .select('id, event_type, session_id, timestamp')
                .order('timestamp', { ascending: false })
                .limit(5);

            if (error) {
                this.logResult(
                    '读取analytics数据',
                    false,
                    `读取失败: ${error.message}`,
                    { code: error.code }
                );
                return false;
            }

            this.logResult(
                '读取analytics数据',
                true,
                `成功读取 ${data.length} 条记录`,
                { count: data.length, latestRecord: data[0] }
            );
            return true;
        } catch (err) {
            this.logResult('读取analytics数据', false, `异常: ${err.message}`);
            return false;
        }
    }

    // 测试4: 验证不能删除analytics数据（应该失败）
    async testDeleteAnalytics() {
        try {
            // 首先插入一条测试数据，然后尝试删除它
            const testData = {
                style_id: null,
                event_type: 'view',
                session_id: `delete-test-${Date.now()}`,
                timestamp: new Date().toISOString(),
                user_agent: 'Delete-Test/1.0',
                ip_hash: 'delete-test-hash'
            };

            // 插入测试数据
            const { data: insertData, error: insertError } = await this.supabase
                .from('analytics')
                .insert([testData])
                .select();

            if (insertError) {
                this.logResult(
                    '删除analytics数据（预期失败）',
                    false,
                    `无法插入测试数据: ${insertError.message}`
                );
                return false;
            }

            const insertedId = insertData[0].id;

            // 尝试删除刚插入的数据
            const { data, error } = await this.supabase
                .from('analytics')
                .delete()
                .eq('id', insertedId);

            if (error) {
                // 删除被阻止是预期的结果
                this.logResult(
                    '删除analytics数据（预期失败）',
                    true,
                    `删除被正确阻止: ${error.message}`,
                    { code: error.code, message: error.message, testedId: insertedId }
                );
                return true;
            } else {
                // 检查是否真的删除了数据
                const { data: checkData } = await this.supabase
                    .from('analytics')
                    .select('id')
                    .eq('id', insertedId);

                if (checkData && checkData.length > 0) {
                    // 数据仍然存在，说明删除被阻止了
                    this.logResult(
                        '删除analytics数据（预期失败）',
                        true,
                        '删除被RLS策略正确阻止（数据仍存在）',
                        { testedId: insertedId, dataStillExists: true }
                    );
                    return true;
                } else {
                    // 数据被删除了，这是不预期的
                    this.logResult(
                        '删除analytics数据（预期失败）',
                        false,
                        '警告: 删除操作未被RLS策略阻止!',
                        { testedId: insertedId, dataDeleted: true }
                    );
                    return false;
                }
            }
        } catch (err) {
            // 异常也可能是预期的（权限不足）
            this.logResult(
                '删除analytics数据（预期失败）',
                true,
                `删除被正确阻止: ${err.message}`
            );
            return true;
        }
    }

    // 测试5: 验证不能更新styles数据（应该失败）
    async testUpdateStyles() {
        try {
            // 获取一个真实存在的style记录
            const { data: existingStyles, error: selectError } = await this.supabase
                .from('styles')
                .select('id, name, slug')
                .limit(1);

            if (selectError || !existingStyles || existingStyles.length === 0) {
                this.logResult(
                    '更新styles数据（预期失败）',
                    false,
                    `无法获取测试数据: ${selectError?.message || '没有数据'}`
                );
                return false;
            }

            const testStyle = existingStyles[0];
            const originalName = testStyle.name;
            const newName = `RLS-Test-Modified-${Date.now()}`;

            // 尝试更新真实存在的记录
            const { data, error } = await this.supabase
                .from('styles')
                .update({ name: newName })
                .eq('id', testStyle.id)
                .select();

            if (error) {
                this.logResult(
                    '更新styles数据（预期失败）',
                    true,
                    `更新被正确阻止: ${error.message}`,
                    { code: error.code, testedId: testStyle.id }
                );
                return true;
            } else {
                // 检查数据是否真的被更新了
                const { data: checkData } = await this.supabase
                    .from('styles')
                    .select('name')
                    .eq('id', testStyle.id)
                    .single();

                if (checkData && checkData.name === originalName) {
                    // 数据没有被更新，说明更新被阻止了
                    this.logResult(
                        '更新styles数据（预期失败）',
                        true,
                        '更新被RLS策略正确阻止（数据未改变）',
                        { testedId: testStyle.id, originalName, currentName: checkData.name }
                    );
                    return true;
                } else {
                    // 数据被更新了，这是不预期的
                    this.logResult(
                        '更新styles数据（预期失败）',
                        false,
                        '警告: 更新操作未被RLS策略阻止!',
                        { testedId: testStyle.id, originalName, newName: checkData?.name }
                    );
                    return false;
                }
            }
        } catch (err) {
            this.logResult(
                '更新styles数据（预期失败）',
                true,
                `更新被正确阻止: ${err.message}`
            );
            return true;
        }
    }

    // 测试6: 验证批量插入analytics数据
    async testBatchInsertAnalytics() {
        const batchData = [
            {
                style_id: null,
                event_type: 'view',
                session_id: `batch-test-1-${Date.now()}`,
                timestamp: new Date().toISOString(),
                user_agent: 'RLS-Batch-Test/1.0',
                ip_hash: 'batch-hash-1'
            },
            {
                style_id: null,
                event_type: 'download',
                session_id: `batch-test-2-${Date.now()}`,
                timestamp: new Date().toISOString(),
                user_agent: 'RLS-Batch-Test/1.0',
                ip_hash: 'batch-hash-2'
            }
        ];

        try {
            const { data, error } = await this.supabase
                .from('analytics')
                .insert(batchData)
                .select();

            if (error) {
                this.logResult(
                    '批量插入analytics数据',
                    false,
                    `批量插入失败: ${error.message}`,
                    { code: error.code, batchSize: batchData.length }
                );
                return false;
            }

            this.logResult(
                '批量插入analytics数据',
                true,
                `成功批量插入 ${data.length} 条记录`,
                { insertedCount: data.length, sessionIds: data.map(d => d.session_id) }
            );
            return true;
        } catch (err) {
            this.logResult('批量插入analytics数据', false, `异常: ${err.message}`);
            return false;
        }
    }

    // 测试7: 验证连接和基本权限
    async testConnection() {
        try {
            // 测试基本连接
            const { data, error } = await this.supabase
                .from('styles')
                .select('count')
                .limit(1);

            if (error) {
                this.logResult(
                    'Supabase连接测试',
                    false,
                    `连接失败: ${error.message}`,
                    { code: error.code }
                );
                return false;
            }

            this.logResult(
                'Supabase连接测试',
                true,
                'Supabase连接正常',
                { url: SUPABASE_URL }
            );
            return true;
        } catch (err) {
            this.logResult('Supabase连接测试', false, `连接异常: ${err.message}`);
            return false;
        }
    }

    // 运行所有测试
    async runAllTests() {
        console.log(chalk.blue.bold('\n🧪 开始RLS策略验证测试...\n'));
        console.log(chalk.gray(`测试目标: ${SUPABASE_URL}`));
        console.log(chalk.gray(`开始时间: ${new Date().toLocaleString('zh-CN')}\n`));

        const tests = [
            { name: '连接测试', fn: () => this.testConnection() },
            { name: '读取styles表', fn: () => this.testReadStyles() },
            { name: '插入analytics数据', fn: () => this.testInsertAnalytics() },
            { name: '读取analytics数据', fn: () => this.testReadAnalytics() },
            { name: '批量插入analytics', fn: () => this.testBatchInsertAnalytics() },
            { name: '删除权限测试', fn: () => this.testDeleteAnalytics() },
            { name: '更新权限测试', fn: () => this.testUpdateStyles() }
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        for (const test of tests) {
            console.log(chalk.yellow(`\n🔍 执行测试: ${test.name}`));
            const result = await test.fn();
            if (result !== false) passedTests++;

            // 测试间隔，避免过快请求
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 输出测试总结
        this.printSummary(passedTests, totalTests);
    }

    // 打印测试总结
    printSummary(passed, total) {
        const duration = Date.now() - this.startTime;
        const successRate = Math.round((passed / total) * 100);

        console.log(chalk.blue.bold('\n📊 测试总结'));
        console.log(chalk.gray('='.repeat(50)));

        console.log(`总测试数: ${total}`);
        console.log(`通过测试: ${chalk.green(passed)}`);
        console.log(`失败测试: ${chalk.red(total - passed)}`);
        console.log(`成功率: ${successRate >= 80 ? chalk.green(successRate + '%') : chalk.red(successRate + '%')}`);
        console.log(`执行时间: ${duration}ms`);
        console.log(`完成时间: ${new Date().toLocaleString('zh-CN')}`);

        // RLS策略建议
        console.log(chalk.blue.bold('\n🛡️ RLS策略状态'));
        console.log(chalk.gray('='.repeat(50)));

        if (successRate >= 80) {
            console.log(chalk.green('✅ RLS策略配置正常'));
            console.log(chalk.green('✅ 数据访问权限符合预期'));
            console.log(chalk.green('✅ 安全策略有效运行'));
        } else {
            console.log(chalk.red('❌ RLS策略可能存在问题'));
            console.log(chalk.yellow('⚠️  建议检查以下项目:'));
            console.log('   - 表是否启用了RLS');
            console.log('   - 策略是否正确创建');
            console.log('   - anon角色权限是否正确');
        }

        // 详细结果
        console.log(chalk.blue.bold('\n📋 详细测试结果'));
        console.log(chalk.gray('='.repeat(50)));

        this.testResults.forEach((result, index) => {
            const status = result.success ? chalk.green('PASS') : chalk.red('FAIL');
            console.log(`${index + 1}. [${status}] ${result.test}`);
            console.log(`   ${chalk.gray(result.message)}`);
        });

        console.log(chalk.blue.bold('\n🔗 相关资源'));
        console.log(chalk.gray('='.repeat(50)));
        console.log(`Supabase Dashboard: ${SUPABASE_URL.replace('https://', 'https://app.supabase.com/project/')}`);
        console.log('RLS文档: https://supabase.com/docs/guides/auth/row-level-security');
        console.log('策略示例: https://supabase.com/docs/guides/auth/row-level-security#policies');
    }
}

// 主函数
async function main() {
    try {
        const tester = new RLSPolicyTester();
        await tester.runAllTests();

        // 根据测试结果设置退出码
        const failedTests = tester.testResults.filter(r => !r.success).length;
        process.exit(failedTests > 0 ? 1 : 0);

    } catch (error) {
        console.error(chalk.red.bold('\n💥 测试脚本执行失败:'));
        console.error(chalk.red(error.message));
        console.error(chalk.gray(error.stack));
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = RLSPolicyTester;
