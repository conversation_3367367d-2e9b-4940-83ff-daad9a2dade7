# 🚀 LoginLab Vue3 技术栈实施指南

## 📋 技术栈选择说明

基于您对 Vue3 技术栈的熟悉程度和 MySQL 数据库的偏好，我们调整了原有的技术架构方案，采用 Vue3 生态系统来构建 LoginLab 平台。

## 🏗️ 核心技术栈

### 前端框架

**核心技术**

- **框架**：Nuxt.js 3.x - Vue3 生态的全栈框架
- **版本**：Vue 3.5+ - 最新版本，支持 Composition API
- **语言**：TypeScript - 强类型支持，提高代码质量
- **构建工具**：Vite - 快速的构建工具和开发服务器

**UI 和样式**

- **CSS 框架**：Tailwind CSS v4 - 最新版本的原子化 CSS
- **组件库**：Element Plus - Vue3 高质量组件库
- **图标库**：@element-plus/icons-vue - 配套图标组件
- **动画库**：@vueuse/motion - Vue3 动画解决方案

**状态管理**

- **状态管理**：Pinia - Vue3 官方推荐的状态管理库
- **持久化**：@pinia/plugin-persistedstate - 状态持久化插件
- **开发工具**：@pinia/nuxt - Nuxt.js 集成插件

**工具库**

- **组合式函数**：@vueuse/core - Vue3 组合式工具库
- **HTTP 客户端**：@nuxt/http - Nuxt.js HTTP 模块
- **表单验证**：@vuelidate/core - Vue3 表单验证库

### 后端和数据库

**后端技术栈**

- **API 服务**：Nuxt.js Server API - 统一技术栈，简化部署
- **数据库**：MySQL 8.0+ - 成熟稳定的关系型数据库
- **ORM 工具**：Prisma - 类型安全的数据库操作
- **缓存系统**：Redis - 高性能缓存和会话存储
- **身份认证**：@sidebase/nuxt-auth - Vue3 身份认证解决方案
- **文件存储**：Vercel Blob / AWS S3 - 云端文件存储服务

## 🚀 项目初始化

### 1. 创建 Nuxt.js 项目

```bash
# 创建新项目
npx nuxi@latest init loginlab
cd loginlab

# 安装TypeScript支持
npm install --save-dev typescript @nuxt/typescript-build

# 安装核心依赖
npm install @nuxtjs/tailwindcss @element-plus/nuxt @pinia/nuxt @nuxtjs/i18n
npm install @vueuse/core @vueuse/nuxt @vueuse/motion
npm install prisma @prisma/client mysql2 redis
npm install @sidebase/nuxt-auth
```

### 2. Nuxt 配置文件

```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  // 开发工具
  devtools: { enabled: true },

  // TypeScript配置
  typescript: {
    strict: true,
    typeCheck: true,
  },

  // CSS框架
  css: ["~/assets/css/main.css"],

  // 模块
  modules: [
    "@nuxtjs/tailwindcss",
    "@element-plus/nuxt",
    "@pinia/nuxt",
    "@vueuse/nuxt",
    "@nuxtjs/i18n",
    "@sidebase/nuxt-auth",
  ],

  // Element Plus配置
  elementPlus: {
    themes: ["dark"],
  },

  // 国际化配置
  i18n: {
    locales: [
      { code: "en", name: "English", file: "en.json" },
      { code: "zh-CN", name: "简体中文", file: "zh-CN.json" },
      { code: "ja", name: "日本語", file: "ja.json" },
    ],
    lazy: true,
    langDir: "locales/",
    defaultLocale: "en",
    strategy: "prefix_except_default",
  },

  // 认证配置
  auth: {
    baseURL: process.env.AUTH_ORIGIN,
    provider: {
      type: "authjs",
    },
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端）
    databaseUrl: process.env.DATABASE_URL,
    redisUrl: process.env.REDIS_URL,
    authSecret: process.env.NUXT_AUTH_SECRET,

    // 公共配置（客户端和服务端）
    public: {
      baseUrl: process.env.BASE_URL || "http://localhost:3000",
      algoliaAppId: process.env.ALGOLIA_APP_ID,
      algoliaApiKey: process.env.ALGOLIA_API_KEY,
    },
  },
});
```

## 📁 项目结构

```
loginlab/
├── assets/                 # 静态资源
│   ├── css/
│   ├── images/
│   └── icons/
├── components/             # Vue组件
│   ├── ui/                # 基础UI组件
│   ├── template/          # 模板相关组件
│   └── layout/            # 布局组件
├── composables/           # 组合式函数
│   ├── useAuth.ts
│   ├── useTemplates.ts
│   └── useSearch.ts
├── layouts/               # 布局文件
│   ├── default.vue
│   └── admin.vue
├── locales/               # 国际化文件
│   ├── en.json
│   ├── zh-CN.json
│   └── ja.json
├── pages/                 # 页面文件
│   ├── index.vue          # 首页
│   ├── templates/         # 模板相关页面
│   └── admin/             # 管理后台
├── plugins/               # 插件
├── prisma/                # 数据库模型
│   └── schema.prisma
├── server/                # 服务端API
│   └── api/
├── stores/                # Pinia状态管理
│   ├── auth.ts
│   ├── templates.ts
│   └── ui.ts
├── types/                 # TypeScript类型定义
└── utils/                 # 工具函数
```

## 🗄️ 数据库设计

### Prisma Schema 配置

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model LoginTemplate {
  id               Int      @id @default(autoincrement())
  title            String   @db.VarChar(255)
  description      String?  @db.Text
  slug             String   @unique @db.VarChar(255)
  previewImageUrl  String?  @db.VarChar(500)
  demoUrl          String?  @db.VarChar(500)
  htmlContent      String   @db.LongText
  cssContent       String   @db.LongText
  jsContent        String?  @db.LongText
  downloadCount    Int      @default(0)
  viewCount        Int      @default(0)
  likeCount        Int      @default(0)
  difficultyLevel  String   @default("medium") @db.VarChar(20)
  isFeatured       Boolean  @default(false)
  isPublished      Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  createdBy        Int?

  // 关联关系
  author           User?    @relation(fields: [createdBy], references: [id])
  categories       TemplateCategory[]
  tags             TemplateTag[]
  favorites        UserFavorite[]
  downloads        DownloadLog[]
  comments         Comment[]

  @@map("login_templates")
}

model User {
  id          Int      @id @default(autoincrement())
  email       String   @unique @db.VarChar(255)
  username    String?  @unique @db.VarChar(100)
  displayName String?  @db.VarChar(255)
  avatarUrl   String?  @db.VarChar(500)
  bio         String?  @db.Text
  websiteUrl  String?  @db.VarChar(500)
  githubUrl   String?  @db.VarChar(500)
  twitterUrl  String?  @db.VarChar(500)
  role        String   @default("user") @db.VarChar(20)
  isVerified  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  templates   LoginTemplate[]
  favorites   UserFavorite[]
  downloads   DownloadLog[]
  comments    Comment[]

  @@map("users")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  icon        String?  @db.VarChar(50)
  color       String?  @db.VarChar(7)
  parentId    Int?
  sortOrder   Int      @default(0)
  isActive    Boolean  @default(true)

  // 关联关系
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  templates   TemplateCategory[]

  @@map("categories")
}

model Tag {
  id         Int    @id @default(autoincrement())
  name       String @unique @db.VarChar(50)
  slug       String @unique @db.VarChar(50)
  color      String? @db.VarChar(7)
  usageCount Int    @default(0)

  // 关联关系
  templates  TemplateTag[]

  @@map("tags")
}

// 关联表
model TemplateCategory {
  templateId Int
  categoryId Int

  template   LoginTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  category   Category      @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([templateId, categoryId])
  @@map("template_categories")
}

model TemplateTag {
  templateId Int
  tagId      Int

  template   LoginTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  tag        Tag           @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([templateId, tagId])
  @@map("template_tags")
}

model UserFavorite {
  userId     Int
  templateId Int
  createdAt  DateTime @default(now())

  user       User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  template   LoginTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@id([userId, templateId])
  @@map("user_favorites")
}

model DownloadLog {
  id         Int      @id @default(autoincrement())
  templateId Int?
  userId     Int?
  ipAddress  String?  @db.VarChar(45)
  userAgent  String?  @db.Text
  createdAt  DateTime @default(now())

  template   LoginTemplate? @relation(fields: [templateId], references: [id])
  user       User?          @relation(fields: [userId], references: [id])

  @@map("download_logs")
}

model Comment {
  id         Int      @id @default(autoincrement())
  templateId Int
  userId     Int
  parentId   Int?
  content    String   @db.Text
  isApproved Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  template   LoginTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user       User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent     Comment?      @relation("CommentReplies", fields: [parentId], references: [id])
  replies    Comment[]     @relation("CommentReplies")

  @@map("comments")
}
```

## 🔧 核心组合式函数

### 模板管理

```typescript
// composables/useTemplates.ts
export const useTemplates = () => {
  const templates = ref<LoginTemplate[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const fetchTemplates = async (params?: {
    page?: number;
    limit?: number;
    category?: string;
    tags?: string[];
    search?: string;
  }) => {
    loading.value = true;
    error.value = null;

    try {
      const { data } = await $fetch("/api/templates", {
        query: params,
      });
      templates.value = data;
    } catch (err) {
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  const downloadTemplate = async (templateId: number) => {
    try {
      const blob = await $fetch(`/api/templates/${templateId}/download`, {
        responseType: "blob",
      });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `template-${templateId}.zip`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (err) {
      throw new Error("下载失败");
    }
  };

  return {
    templates: readonly(templates),
    loading: readonly(loading),
    error: readonly(error),
    fetchTemplates,
    downloadTemplate,
  };
};
```

### 搜索功能

```typescript
// composables/useSearch.ts
export const useSearch = () => {
  const searchQuery = ref("");
  const filters = ref({
    categories: [],
    tags: [],
    difficulty: "",
    technology: [],
  });
  const results = ref([]);
  const loading = ref(false);

  const search = async () => {
    loading.value = true;

    try {
      const { data } = await $fetch("/api/search", {
        query: {
          q: searchQuery.value,
          ...filters.value,
        },
      });
      results.value = data;
    } catch (err) {
      console.error("搜索失败:", err);
    } finally {
      loading.value = false;
    }
  };

  // 防抖搜索
  const debouncedSearch = useDebounceFn(search, 300);

  watch(searchQuery, debouncedSearch);
  watch(filters, search, { deep: true });

  return {
    searchQuery,
    filters,
    results: readonly(results),
    loading: readonly(loading),
    search,
  };
};
```

## 🎨 组件示例

### 模板卡片组件

```vue
<!-- components/template/TemplateCard.vue -->
<template>
  <div class="template-card group cursor-pointer" @click="viewTemplate">
    <div class="relative overflow-hidden rounded-lg">
      <NuxtImg
        :src="template.previewImageUrl"
        :alt="template.title"
        class="w-full h-48 object-cover transition-transform group-hover:scale-105"
        loading="lazy"
      />
      <div
        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all"
      >
        <div
          class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <el-button circle size="small" @click.stop="toggleFavorite">
            <el-icon><Star /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <div class="p-4">
      <h3 class="font-semibold text-lg mb-2 line-clamp-2">
        {{ template.title }}
      </h3>
      <p class="text-gray-600 text-sm mb-3 line-clamp-2">
        {{ template.description }}
      </p>

      <div class="flex flex-wrap gap-1 mb-3">
        <el-tag
          v-for="tag in template.tags.slice(0, 3)"
          :key="tag.id"
          size="small"
          type="info"
        >
          {{ tag.name }}
        </el-tag>
      </div>

      <div class="flex items-center justify-between text-sm text-gray-500">
        <div class="flex items-center gap-4">
          <span
            ><el-icon><Download /></el-icon> {{ template.downloadCount }}</span
          >
          <span
            ><el-icon><View /></el-icon> {{ template.viewCount }}</span
          >
        </div>
        <el-button size="small" type="primary" @click.stop="downloadTemplate">
          下载
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Star, Download, View } from "@element-plus/icons-vue";

interface Props {
  template: LoginTemplate;
}

const props = defineProps<Props>();
const { downloadTemplate: download } = useTemplates();

const viewTemplate = () => {
  navigateTo(`/templates/${props.template.slug}`);
};

const downloadTemplate = async () => {
  try {
    await download(props.template.id);
    ElMessage.success("下载成功");
  } catch (err) {
    ElMessage.error("下载失败");
  }
};

const toggleFavorite = () => {
  // 收藏功能实现
};
</script>

<style scoped>
.template-card {
  @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
```

---

_本文档详细说明了 LoginLab 项目采用 Vue3 技术栈的实施方案，为开发团队提供具体的技术指导。_

_最后更新：2025 年 8 月 8 日_
