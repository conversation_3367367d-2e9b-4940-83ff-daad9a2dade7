* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: #ffffff;
    color: #333333;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
    color: #666666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.back-button:hover {
    background: #f8f8f8;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 装饰元素 */
.decoration-elements {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-circle {
    position: absolute;
    border: 1px solid #f0f0f0;
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 60px;
    height: 60px;
    top: 70%;
    right: 15%;
    animation-delay: 2s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(240, 240, 240, 0.3) 1px, transparent 1px),
        linear-gradient(90deg, rgba(240, 240, 240, 0.3) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.5;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 表单容器 */
.form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    transition: all 0.5s ease;
    opacity: 1;
    visibility: visible;
}

.form-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.minimal-card {
    background: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.minimal-card:hover {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.1);
}

.minimal-card h2 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: 300;
    color: #333333;
    letter-spacing: -0.5px;
}

.subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: #888888;
    font-size: 14px;
    font-weight: 400;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-group input {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: none;
    background: transparent;
    font-size: 16px;
    color: #333333;
    outline: none;
    transition: all 0.3s ease;
}

.form-group label {
    position: absolute;
    top: 16px;
    left: 12px;
    color: #999999;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: 0;
    left: 8px;
    font-size: 12px;
    color: #333333;
    font-weight: 500;
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #e0e0e0;
    transition: all 0.3s ease;
}

.input-line::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #333333;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.form-group input:focus ~ .input-line::after {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 8px 16px;
    background: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    color: #666666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.verify-btn:hover {
    background: #f0f0f0;
    border-color: #d0d0d0;
}

/* 登录按钮 */
.login-btn {
    width: 100%;
    padding: 16px;
    margin: 32px 0 24px 0;
    background: #333333;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    background: #222222;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.login-btn:active {
    transform: translateY(0);
}

/* 附加链接 */
.additional-links {
    text-align: center;
    font-size: 14px;
}

.additional-links a {
    color: #666666;
    text-decoration: none;
    transition: all 0.3s ease;
}

.additional-links a:hover {
    color: #333333;
    text-decoration: underline;
}

.divider {
    margin: 0 12px;
    color: #cccccc;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .form-container {
        padding: 16px;
    }
    
    .minimal-card {
        padding: 24px;
    }
    
    .minimal-card h2 {
        font-size: 24px;
    }
    
    .back-button {
        top: 16px;
        left: 16px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .floating-circle {
        display: none;
    }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.minimal-card {
    animation: fadeInUp 0.6s ease-out;
}
