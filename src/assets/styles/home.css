* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.content {
    flex: 1;
    padding: 20px;
}

.bottom-nav {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 400px;
    height: 70px;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 0 15px;
}

.nav-indicator {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 25%;
    background: linear-gradient(45deg, #6ac1c5, #bda5ff);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
    opacity: 0.15;
    z-index: 1;
}

.nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 25%;
    height: 100%;
    z-index: 2;
    cursor: pointer;
    transition: all 0.3s;
}

.nav-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    transform-origin: center;
    transition: all 0.3s;
}

.nav-icon svg {
    width: 24px;
    height: 24px;
    stroke: #666;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    transition: all 0.3s;
}

.nav-text {
    font-size: 12px;
    font-weight: 500;
    color: #666;
    margin-top: 4px;
    transition: all 0.3s;
}

.nav-item.active .nav-icon {
    /* transform: translateY(-2px); */
}

.nav-item.active .nav-icon svg {
    stroke: #6ac1c5;
    transform: scale(1.1);
}

.nav-item.active .nav-text {
    color: #6ac1c5;
    font-weight: 600;
}

.nav-item:hover .nav-icon {
    transform: translateZ(20px);
}

.nav-item:hover .nav-text {
    color: #6ac1c5;
}


.nav-item:active::after {
    opacity: 1;
    transform: scale(1.5);
    transition: all 0.2s ease-out;
}

/* SVG 描边动画 */
.nav-icon svg {
    stroke-dasharray: 50;
    stroke-dashoffset: 0;
}

.nav-item.active .nav-icon svg {
    animation: stroke 1s ease forwards;
}

@keyframes stroke {
    0% {
        stroke-dashoffset: 50;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

/* 微交互动效 */
.nav-item {
    transform-style: preserve-3d;
    perspective: 1000px;
}

.nav-icon {
    transform: translateZ(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover .nav-icon {
    transform: translateZ(20px);
}

.nav-text {
    transform: translateZ(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover .nav-text {
    transform: translateZ(10px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 添加图标描边渐变效果 */
.nav-item.active .nav-icon svg {
    stroke: url(#gradient);
} 