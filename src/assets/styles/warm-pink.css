* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #ff6b9d;
    border-radius: 25px;
    color: #ff6b9d;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.back-button:hover {
    background: #ff6b9d;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
}

/* 花瓣飘落背景 */
.background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.petal-container {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.petal {
    position: absolute;
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50% 0 50% 0;
    animation: petalFall 8s linear infinite;
    opacity: 0.7;
}

.petal:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.petal:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 9s;
}

.petal:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.petal:nth-child(4) {
    left: 40%;
    animation-delay: 3s;
    animation-duration: 10s;
}

.petal:nth-child(5) {
    left: 60%;
    animation-delay: 1.5s;
    animation-duration: 8.5s;
}

.petal:nth-child(6) {
    left: 70%;
    animation-delay: 2.5s;
    animation-duration: 9.5s;
}

.petal:nth-child(7) {
    left: 80%;
    animation-delay: 0.5s;
    animation-duration: 7.5s;
}

.petal:nth-child(8) {
    left: 90%;
    animation-delay: 3.5s;
    animation-duration: 8.8s;
}

@keyframes petalFall {
    0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(calc(100vh + 100px)) rotate(360deg);
        opacity: 0;
    }
}

.heart-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.heart {
    position: absolute;
    width: 20px;
    height: 20px;
    opacity: 0.3;
    animation: heartFloat 6s ease-in-out infinite;
}

.heart:before,
.heart:after {
    content: '';
    width: 10px;
    height: 16px;
    position: absolute;
    left: 10px;
    transform: rotate(-45deg);
    background: #ff6b9d;
    border-radius: 10px 10px 0 0;
    transform-origin: 0 100%;
}

.heart:after {
    left: 0;
    transform: rotate(45deg);
    transform-origin: 100% 100%;
}

.heart:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.heart:nth-child(2) {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.heart:nth-child(3) {
    bottom: 30%;
    left: 70%;
    animation-delay: 4s;
}

@keyframes heartFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.6;
    }
}

/* 粉色卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.pink-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 40px 30px;
    box-shadow: 
        0 20px 40px rgba(255, 107, 157, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.5);
    animation: cardSlideUp 0.8s ease-out;
}

@keyframes cardSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.flower-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: flowerSpin 3s ease-in-out infinite;
}

@keyframes flowerSpin {
    0%, 100% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(10deg) scale(1.1); }
}

.card-header h2 {
    color: #ff6b9d;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px rgba(255, 107, 157, 0.3);
}

.subtitle {
    color: rgba(255, 107, 157, 0.7);
    font-size: 0.9rem;
    margin-bottom: 20px;
    font-weight: 300;
    font-style: italic;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(255, 107, 157, 0.3);
    color: #333;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: #ff6b9d;
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 15px;
    color: rgba(255, 107, 157, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -5px;
    font-size: 12px;
    color: #ff6b9d;
    font-weight: 500;
}

.input-decoration {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff6b9d, #ff8fab);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.form-group input:focus ~ .input-decoration {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn, .pink-btn-small {
    padding: 12px 20px;
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.verify-btn:hover:not(:disabled), .pink-btn-small:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
}

.verify-btn:disabled, .pink-btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 登录按钮 */
.login-btn, .pink-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #ff6b9d, #ff8fab);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
}

.login-btn:hover, .pink-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 157, 0.4);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-hearts {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.heart-icon {
    position: absolute;
    font-size: 12px;
    animation: heartBounce 0.6s ease-in-out;
}

.heart-icon:nth-child(1) {
    left: -20px;
    animation-delay: 0s;
}

.heart-icon:nth-child(2) {
    left: 0px;
    animation-delay: 0.1s;
}

.heart-icon:nth-child(3) {
    left: 20px;
    animation-delay: 0.2s;
}

.login-btn:active .btn-hearts, .pink-btn:active .btn-hearts {
    opacity: 1;
}

@keyframes heartBounce {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.2); }
    100% { transform: translateY(0) scale(1); }
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.pink-link {
    color: #ff6b9d;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.pink-link:hover {
    color: #ff8fab;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.5);
}

.divider {
    color: rgba(255, 107, 157, 0.5);
    margin: 0 15px;
    font-weight: bold;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .pink-card {
        padding: 30px 20px;
        margin: 0 15px;
    }
    
    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .verification-group {
        gap: 8px;
    }

    .verification-group .form-group {
        flex: 1;
        min-width: 0;
    }

    .verify-btn, .pink-btn-small {
        flex-shrink: 0;
        padding: 12px 10px;
        font-size: 12px;
        min-width: 75px;
    }
    
    .flower-icon {
        font-size: 2.5rem;
    }
}
