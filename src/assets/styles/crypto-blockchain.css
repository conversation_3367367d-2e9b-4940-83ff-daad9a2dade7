* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: #0a0a0a;
    color: #00ff88;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(0, 255, 136, 0.1);
    border: 2px solid #00ff88;
    border-radius: 25px;
    color: #00ff88;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

.back-button:hover {
    background: rgba(0, 255, 136, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 30px rgba(0, 255, 136, 0.5);
}

/* 区块链装饰元素 */
.blockchain-decoration {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

/* 区块链网络画布 */
.blockchain-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

/* 加密货币符号 */
.crypto-symbols {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.crypto-symbol {
    position: absolute;
    font-size: 2rem;
    font-weight: bold;
    opacity: 0.1;
    animation: float-crypto 10s ease-in-out infinite;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
}

.crypto-symbol.btc {
    top: 20%;
    left: 10%;
    color: #f7931a;
    animation-delay: 0s;
}

.crypto-symbol.eth {
    top: 60%;
    right: 15%;
    color: #627eea;
    animation-delay: 2s;
}

.crypto-symbol.ada {
    bottom: 30%;
    left: 20%;
    color: #0033ad;
    animation-delay: 4s;
}

.crypto-symbol.dot {
    top: 40%;
    left: 70%;
    color: #e6007a;
    animation-delay: 6s;
}

@keyframes float-crypto {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.1; }
    25% { transform: translateY(-20px) rotate(90deg); opacity: 0.3; }
    50% { transform: translateY(10px) rotate(180deg); opacity: 0.2; }
    75% { transform: translateY(-15px) rotate(270deg); opacity: 0.4; }
}

/* 数据区块动画 */
.data-blocks {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.data-block {
    position: absolute;
    padding: 8px 12px;
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid #00ff88;
    border-radius: 8px;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
    color: #00ff88;
    animation: blockchain-flow 8s linear infinite;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

.data-block.block-1 {
    top: 10%;
    right: 5%;
    animation-delay: 0s;
}

.data-block.block-2 {
    top: 25%;
    left: 5%;
    animation-delay: 2s;
}

.data-block.block-3 {
    bottom: 20%;
    right: 10%;
    animation-delay: 4s;
}

.data-block.block-4 {
    bottom: 35%;
    left: 8%;
    animation-delay: 6s;
}

@keyframes blockchain-flow {
    0% { transform: translateX(-100px); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(100px); opacity: 0; }
}

/* 数字化粒子效果 */
.digital-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #00ff88;
    border-radius: 50%;
    animation: particle-flow 6s linear infinite;
    box-shadow: 0 0 6px rgba(0, 255, 136, 0.8);
}

@keyframes particle-flow {
    0% { transform: translateY(100vh) translateX(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100px) translateX(50px); opacity: 0; }
}

/* 密码学图形装饰 */
.cryptography-ornaments {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.cryptography-ornaments > div {
    position: absolute;
    font-size: 3rem;
    opacity: 0.1;
    animation: pulse-crypto 4s ease-in-out infinite;
}

.crypto-key {
    top: 15%;
    right: 25%;
    animation-delay: 0s;
}

.crypto-lock {
    bottom: 25%;
    right: 30%;
    animation-delay: 1s;
}

.crypto-shield {
    top: 50%;
    left: 15%;
    animation-delay: 2s;
}

@keyframes pulse-crypto {
    0%, 100% { transform: scale(1); opacity: 0.1; }
    50% { transform: scale(1.2); opacity: 0.3; }
}

/* 网格背景 */
.crypto-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
    background-size: 40px 40px;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(40px, 40px); }
}

/* 登录容器 */
.login-container {
    z-index: 10;
    width: 90%;
    max-width: 450px;
    padding: 20px;
}

/* 区块链卡片 */
.crypto-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #00ff88;
    border-radius: 20px;
    padding: 40px 30px;
    backdrop-filter: blur(20px);
    box-shadow: 
        0 20px 40px rgba(0, 255, 136, 0.2),
        inset 0 0 20px rgba(0, 255, 136, 0.1);
    animation: card-glow 3s ease-in-out infinite alternate;
}

@keyframes card-glow {
    0% { box-shadow: 0 20px 40px rgba(0, 255, 136, 0.2), inset 0 0 20px rgba(0, 255, 136, 0.1); }
    100% { box-shadow: 0 25px 60px rgba(0, 255, 136, 0.4), inset 0 0 30px rgba(0, 255, 136, 0.2); }
}

/* 区块链图标 */
.blockchain-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.cube {
    position: relative;
    width: 60px;
    height: 60px;
    transform-style: preserve-3d;
    animation: rotate-cube 4s infinite linear;
}

@keyframes rotate-cube {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    100% { transform: rotateX(360deg) rotateY(360deg); }
}

.face {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(0, 255, 136, 0.2);
    border: 1px solid #00ff88;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.face.front { transform: translateZ(30px); }
.face.back { transform: translateZ(-30px) rotateY(180deg); }
.face.top { transform: rotateX(90deg) translateZ(30px); }

/* 标题 */
.crypto-card h2 {
    text-align: center;
    color: #00ff88;
    font-size: 1.8rem;
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
    font-weight: 700;
}

.subtitle {
    text-align: center;
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 25px;
    font-family: 'Courier New', monospace;
    opacity: 0.8;
}

/* 哈希验证指示器 */
.hash-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid #00ff88;
    border-radius: 8px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
}

.hash-hash {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #00ff88;
    opacity: 0.8;
}

.hash-status {
    font-size: 0.8rem;
    color: #00ff88;
    display: flex;
    align-items: center;
    gap: 5px;
}

.hash-status.active {
    color: #00ff88;
    animation: blink 2s ease-in-out infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 表单样式 */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    position: relative;
}

.input-label {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.label-icon {
    color: #00ff88;
    font-size: 1.1rem;
}

.input-label label {
    color: #00ff88;
    font-size: 0.9rem;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.form-group input {
    width: 100%;
    padding: 15px 20px;
    background: rgba(0, 255, 136, 0.05);
    border: 2px solid rgba(0, 255, 136, 0.3);
    border-radius: 12px;
    color: #00ff88;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-group input:focus {
    outline: none;
    border-color: #00ff88;
    background: rgba(0, 255, 136, 0.1);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
    transform: translateY(-2px);
}

.form-group input::placeholder {
    color: rgba(0, 255, 136, 0.3);
}

.input-decoration {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #00ff88, #00ccff);
    transition: width 0.3s ease;
}

.form-group:focus-within .input-decoration {
    width: 100%;
}

/* 二维码扫描区域 */
.qr-section {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: rgba(0, 255, 136, 0.05);
    border: 2px dashed rgba(0, 255, 136, 0.3);
    border-radius: 12px;
    margin: 10px 0;
}

.qr-code-container {
    text-align: center;
}

.qr-code {
    width: 80px;
    height: 80px;
    background: rgba(0, 255, 136, 0.1);
    border: 2px solid #00ff88;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    animation: qr-scan 2s ease-in-out infinite;
}

@keyframes qr-scan {
    0%, 100% { transform: scale(1); box-shadow: 0 0 10px rgba(0, 255, 136, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
}

.qr-placeholder {
    font-size: 2rem;
    animation: placeholder-pulse 1.5s ease-in-out infinite;
}

@keyframes placeholder-pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.qr-text {
    color: #888;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
}

/* 安全等级显示 */
.security-level {
    margin: 15px 0;
}

.security-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: rgba(0, 255, 136, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.security-bar {
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #00ff88, #00ccff);
    border-radius: 2px;
}

.security-indicator.high .security-text {
    color: #00ff88;
}

.security-text {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #888;
}

/* 登录按钮 */
.login-btn {
    position: relative;
    padding: 18px 30px;
    background: linear-gradient(135deg, #00ff88, #00ccff);
    border: none;
    border-radius: 12px;
    color: #000;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    font-family: 'Courier New', monospace;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.5);
}

.login-btn:active {
    transform: translateY(-1px);
}

.btn-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.login-btn:hover .btn-effect {
    left: 100%;
}

/* 验证按钮 */
.verify-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    padding: 8px 16px;
    background: rgba(0, 255, 136, 0.2);
    border: 1px solid #00ff88;
    border-radius: 6px;
    color: #00ff88;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.verify-btn:hover {
    background: rgba(0, 255, 136, 0.3);
    transform: translateY(-50%) scale(1.05);
}

/* 附加链接 */
.additional-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 25px;
}

.additional-links a {
    color: #00ff88;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
}

.additional-links a:hover {
    opacity: 0.7;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.divider {
    color: #444;
}

/* 特殊组件 */
.wallet-address-preview,
.mnemonic-verification {
    margin: 15px 0;
    padding: 15px;
    background: rgba(0, 255, 136, 0.05);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 8px;
}

.address-label,
.mnemonic-label {
    color: #00ff88;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    margin-bottom: 8px;
}

.address-preview {
    color: #00ff88;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    opacity: 0.8;
}

.mnemonic-field {
    width: 100%;
    padding: 12px 16px;
    background: rgba(0, 255, 136, 0.05);
    border: 2px solid rgba(0, 255, 136, 0.3);
    border-radius: 8px;
    color: #00ff88;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.mnemonic-field:focus {
    outline: none;
    border-color: #00ff88;
    background: rgba(0, 255, 136, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .crypto-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .crypto-card h2 {
        font-size: 1.5rem;
    }
    
    .login-btn {
        padding: 16px 25px;
        font-size: 1rem;
    }
    
    .qr-section {
        padding: 15px;
    }
    
    .qr-code {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 480px) {
    .login-container {
        width: 95%;
        padding: 10px;
    }
    
    .crypto-card {
        padding: 25px 15px;
    }
    
    .form-group input {
        padding: 12px 16px;
        font-size: 0.9rem;
    }
    
    .verify-btn {
        padding: 6px 12px;
        font-size: 0.7rem;
    }
}