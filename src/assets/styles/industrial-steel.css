* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial Black', 'Impact', Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #4a5568 50%, #2d3748 75%, #1a202c 100%);
    color: #e2e8f0;
    overflow: hidden;
}

.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border: 2px solid #718096;
    border-radius: 0;
    color: #e2e8f0;
    text-decoration: none;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.back-button:hover {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.industrial-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.steel-panel {
    position: absolute;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border: 1px solid #718096;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    animation: steelFloat 10s ease-in-out infinite;
}

.panel-1 {
    width: 150px;
    height: 200px;
    top: 15%;
    left: 8%;
    animation-delay: 0s;
}

.panel-2 {
    width: 120px;
    height: 180px;
    top: 65%;
    right: 12%;
    animation-delay: 5s;
}

.steel-beam {
    position: absolute;
    background: linear-gradient(90deg, #2d3748, #4a5568, #2d3748);
    border: 1px solid #718096;
    animation: beamMove 8s ease-in-out infinite;
}

.beam-1 {
    width: 200px;
    height: 20px;
    top: 30%;
    left: 25%;
    animation-delay: 0s;
}

.beam-2 {
    width: 150px;
    height: 15px;
    bottom: 35%;
    right: 20%;
    animation-delay: 4s;
}

.rivet {
    position: absolute;
    font-size: 12px;
    color: #718096;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    animation: rivetPulse 6s ease-in-out infinite;
}

.rivet-1 { top: 20%; left: 15%; animation-delay: 0s; }
.rivet-2 { top: 70%; right: 18%; animation-delay: 2s; }
.rivet-3 { bottom: 25%; left: 30%; animation-delay: 4s; }

.pipe {
    position: absolute;
    background: linear-gradient(45deg, #4a5568, #2d3748);
    border: 2px solid #718096;
    border-radius: 50px;
    animation: pipeVibrate 4s ease-in-out infinite;
}

.pipe-1 {
    width: 15px;
    height: 100px;
    top: 25%;
    right: 8%;
    animation-delay: 0s;
}

.pipe-2 {
    width: 12px;
    height: 80px;
    bottom: 20%;
    left: 12%;
    animation-delay: 2s;
}

.steam-effect {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(226, 232, 240, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: steamRise 3s ease-in-out infinite;
}

.steam-1 { top: 20%; right: 10%; animation-delay: 0s; }
.steam-2 { bottom: 15%; left: 15%; animation-delay: 1.5s; }

@keyframes steelFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes beamMove {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(5px); }
}

@keyframes rivetPulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

@keyframes pipeVibrate {
    0%, 100% { transform: translateX(0px); }
    25% { transform: translateX(1px); }
    75% { transform: translateX(-1px); }
}

@keyframes steamRise {
    0% { opacity: 0.3; transform: translateY(0px) scale(1); }
    100% { opacity: 0; transform: translateY(-30px) scale(1.5); }
}

.form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    transition: all 0.5s ease;
    opacity: 1;
    visibility: visible;
}

.form-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.industrial-card {
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border: 3px solid #718096;
    border-radius: 0;
    padding: 40px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.industrial-card::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 1px solid rgba(113, 128, 150, 0.3);
    pointer-events: none;
}

.industrial-card:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.industrial-card h2 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: bold;
    color: #e2e8f0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 2px;
    text-transform: uppercase;
}

.subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: #a0aec0;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-group input {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: none;
    background: linear-gradient(145deg, #2d3748, #4a5568);
    font-size: 16px;
    color: #e2e8f0;
    outline: none;
    border-radius: 0;
    transition: all 0.3s ease;
    font-family: 'Arial Black', Arial, sans-serif;
    border: 2px solid #718096;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

.form-group input:focus {
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border-color: #a0aec0;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(160, 174, 192, 0.3);
}

.form-group label {
    position: absolute;
    top: 16px;
    left: 12px;
    color: #a0aec0;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    font-family: 'Arial Black', Arial, sans-serif;
    font-weight: bold;
    text-transform: uppercase;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -8px;
    left: 8px;
    font-size: 12px;
    color: #e2e8f0;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    padding: 0 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.steel-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid transparent;
    background: linear-gradient(45deg, #a0aec0, #718096) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-group input:focus ~ .steel-border {
    opacity: 1;
    box-shadow: 0 0 20px rgba(160, 174, 192, 0.5);
}

.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 16px 20px;
    background: linear-gradient(145deg, #718096, #4a5568);
    border: 2px solid #a0aec0;
    border-radius: 0;
    color: #e2e8f0;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-family: 'Arial Black', Arial, sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.verify-btn:hover {
    background: linear-gradient(145deg, #a0aec0, #718096);
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.industrial-btn {
    width: 100%;
    padding: 16px;
    margin: 32px 0 24px 0;
    background: linear-gradient(145deg, #718096, #4a5568);
    color: #e2e8f0;
    border: 3px solid #a0aec0;
    border-radius: 0;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: 'Arial Black', Arial, sans-serif;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.industrial-btn:hover {
    background: linear-gradient(145deg, #a0aec0, #718096);
    transform: translateY(-3px);
    box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.steel-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: all 0.5s ease;
}

.industrial-btn:hover .steel-effect {
    left: 100%;
}

.additional-links {
    text-align: center;
    font-size: 14px;
}

.additional-links a {
    color: #a0aec0;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: bold;
    text-transform: uppercase;
}

.additional-links a:hover {
    color: #e2e8f0;
    text-shadow: 0 0 10px rgba(226, 232, 240, 0.5);
}

.divider {
    margin: 0 12px;
    color: #718096;
}

@media (max-width: 480px) {
    .form-container {
        padding: 16px;
    }
    
    .industrial-card {
        padding: 24px;
    }
    
    .industrial-card h2 {
        font-size: 24px;
    }
    
    .back-button {
        top: 16px;
        left: 16px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .steel-panel, .steel-beam, .pipe {
        display: none;
    }
}

@keyframes industrialFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
        filter: brightness(0.5);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: brightness(1);
    }
}

.industrial-card {
    animation: industrialFadeIn 1s ease-out;
}
