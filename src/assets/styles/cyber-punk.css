/* 赛博朋克风格 - Cyber Punk Style */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #000;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(138, 43, 226, 0.2);
    border: 1px solid #8a2be2;
    border-radius: 0;
    color: #8a2be2;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.back-button:hover {
    background: rgba(138, 43, 226, 0.3);
    border-color: #ff1493;
    color: #ff1493;
    transform: translateY(-2px);
    box-shadow: 0 0 25px rgba(255, 20, 147, 0.7);
}

.back-button svg {
    width: 16px;
    height: 16px;
}

.container {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 背景 - 赛博朋克网格 */
.background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #0a0014 0%, #1a0033 50%, #0a0014 100%);
    overflow: hidden;
}

.cyber-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(138, 43, 226, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(138, 43, 226, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 10s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* 故障线条 */
.glitch-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff00ff, #00ffff, transparent);
    animation: glitchLine 3s linear infinite;
}

.line1 { top: 20%; animation-delay: 0s; }
.line2 { top: 50%; animation-delay: 1s; }
.line3 { top: 80%; animation-delay: 2s; }

@keyframes glitchLine {
    0% { left: -100%; opacity: 0; }
    50% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* 霓虹文字 */
.neon-text {
    position: absolute;
    font-size: 60px;
    font-weight: 900;
    color: transparent;
    -webkit-text-stroke: 2px #ff00ff;
    text-shadow: 
        0 0 20px #ff00ff,
        0 0 40px #ff00ff,
        0 0 60px #ff00ff;
    opacity: 0.1;
    animation: neonFlicker 2s infinite;
}

.text1 { top: 10%; left: 10%; }
.text2 { bottom: 10%; right: 10%; }

@keyframes neonFlicker {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.3; }
}

/* 数据流 */
.data-stream {
    position: absolute;
    font-family: monospace;
    color: #00ff00;
    font-size: 14px;
    opacity: 0.3;
    animation: dataFlow 5s linear infinite;
}

.stream1 { top: 30%; left: 5%; }
.stream2 { top: 60%; right: 10%; }
.stream3 { bottom: 20%; left: 40%; }

@keyframes dataFlow {
    0% { transform: translateY(-100px); opacity: 0; }
    50% { opacity: 0.3; }
    100% { transform: translateY(100vh); opacity: 0; }
}

/* 登录容器样式 */
.login-container,
.register-container,
.forgot-container {
    background: rgba(10, 0, 20, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid #ff00ff;
    padding: 40px;
    width: 90%;
    max-width: 400px;
    position: relative;
    z-index: 10;
    display: block;
    animation: cyberSlideIn 0.5s ease-out;
    clip-path: polygon(
        0 20px, 20px 0,
        calc(100% - 20px) 0, 100% 20px,
        100% calc(100% - 20px), calc(100% - 20px) 100%,
        20px 100%, 0 calc(100% - 20px)
    );
    box-shadow:
        0 0 50px rgba(255, 0, 255, 0.5),
        inset 0 0 50px rgba(138, 43, 226, 0.1);
}

.login-container.hidden,
.register-container.hidden,
.forgot-container.hidden {
    display: none;
}

.login-container.active,
.register-container.active,
.forgot-container.active {
    display: block;
}

@keyframes cyberSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 故障标题效果 */
.glitch-wrapper {
    position: relative;
}

.form-title {
    text-align: center;
    color: #ff00ff;
    font-size: 28px;
    margin-bottom: 5px;
    font-weight: 900;
    text-transform: uppercase;
    position: relative;
    animation: glitchAnimation 2s infinite;
}

.form-title.glitch::before,
.form-title.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.form-title.glitch::before {
    animation: glitch1 0.5s infinite;
    color: #00ffff;
    z-index: -1;
}

.form-title.glitch::after {
    animation: glitch2 0.5s infinite;
    color: #ff00ff;
    z-index: -2;
}

@keyframes glitch1 {
    0%, 100% { clip-path: inset(0 0 0 0); }
    20% { clip-path: inset(20% 0 30% 0); transform: translateX(2px); }
    40% { clip-path: inset(50% 0 20% 0); transform: translateX(-2px); }
    60% { clip-path: inset(10% 0 60% 0); transform: translateX(1px); }
}

@keyframes glitch2 {
    0%, 100% { clip-path: inset(0 0 0 0); }
    20% { clip-path: inset(60% 0 10% 0); transform: translateX(-1px); }
    40% { clip-path: inset(20% 0 40% 0); transform: translateX(2px); }
    60% { clip-path: inset(30% 0 30% 0); transform: translateX(-2px); }
}

.form-subtitle {
    text-align: center;
    color: #00ff00;
    margin-bottom: 30px;
    font-size: 12px;
    font-family: monospace;
    letter-spacing: 2px;
}

/* 表单组 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 12px 0;
    font-size: 14px;
    font-family: 'Orbitron', monospace;
    border: none;
    border-bottom: 2px solid #444;
    background: transparent;
    outline: none;
    transition: all 0.3s;
    color: #00ffff;
}

.form-group label {
    position: absolute;
    top: 12px;
    left: 0;
    font-size: 12px;
    color: #888;
    pointer-events: none;
    transition: all 0.3s;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #ff00ff, #00ffff);
    transform: scaleX(0);
    transition: transform 0.3s;
}

.form-group input:focus ~ .input-line,
.form-group input:not(:placeholder-shown) ~ .input-line {
    transform: scaleX(1);
    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.5);
}

.form-group input:focus ~ label,
.form-group input:not(:placeholder-shown) ~ label {
    transform: translateY(-25px);
    font-size: 10px;
    color: #00ffff;
}

/* 赛博角装饰 */
.cyber-corner {
    position: absolute;
    width: 10px;
    height: 10px;
    border: 2px solid #00ffff;
}

.corner-tl {
    top: -5px;
    left: -5px;
    border-right: none;
    border-bottom: none;
}

.corner-br {
    bottom: -5px;
    right: -5px;
    border-left: none;
    border-top: none;
}

/* 验证码组 */
.verification-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.verification-group input {
    flex: 1;
}

.cyber-code-btn {
    padding: 10px 15px;
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
    cursor: pointer;
    font-size: 10px;
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    white-space: nowrap;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.cyber-code-btn:hover {
    color: #000;
    background: #00ffff;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

.cyber-code-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 表单选项 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.cyber-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #888;
    font-size: 12px;
}

.cyber-checkbox input {
    display: none;
}

.cyber-checkbox .checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid #00ffff;
    position: relative;
    transition: all 0.3s;
}

.cyber-checkbox input:checked ~ .checkmark {
    background: #00ffff;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.cyber-checkbox input:checked ~ .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-size: 10px;
}

.forgot-password {
    color: #ff00ff;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.3s;
}

.forgot-password:hover {
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.glitch-text {
    position: relative;
    display: inline-block;
}

/* 提交按钮 */
.cyber-btn {
    width: 100%;
    padding: 14px;
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
    font-size: 14px;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    clip-path: polygon(10px 0, 100% 0, 100% calc(100% - 10px), calc(100% - 10px) 100%, 0 100%, 0 10px);
}

.cyber-btn:hover {
    background: #00ffff;
    color: #000;
    box-shadow: 
        0 0 30px rgba(0, 255, 255, 0.8),
        inset 0 0 20px rgba(255, 0, 255, 0.3);
    transform: scale(1.05);
}

.btn-glitch {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 255, 0.4), transparent);
    transition: left 0.5s;
}

.cyber-btn:hover .btn-glitch {
    left: 100%;
}

/* 分隔线 */
.cyber-divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
    color: #ff00ff;
    font-size: 12px;
    font-family: monospace;
}

.cyber-divider::before,
.cyber-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 35%;
    height: 1px;
    background: linear-gradient(90deg, transparent, #ff00ff);
}

.cyber-divider::before {
    left: 0;
}

.cyber-divider::after {
    right: 0;
    background: linear-gradient(90deg, #ff00ff, transparent);
}

/* 社交登录 */
.social-login {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.social-btn {
    flex: 1;
    padding: 10px;
    background: transparent;
    border: 1px solid #444;
    color: #888;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    transition: all 0.3s;
    font-size: 11px;
    font-family: 'Orbitron', monospace;
}

.neon-purple {
    border-color: #ff00ff;
    color: #ff00ff;
}

.neon-cyan {
    border-color: #00ffff;
    color: #00ffff;
}

.social-btn:hover {
    transform: translateY(-2px);
}

.neon-purple:hover {
    background: #ff00ff;
    color: #000;
    box-shadow: 0 5px 20px rgba(255, 0, 255, 0.6);
}

.neon-cyan:hover {
    background: #00ffff;
    color: #000;
    box-shadow: 0 5px 20px rgba(0, 255, 255, 0.6);
}

/* 切换表单 */
.switch-form {
    text-align: center;
    color: #888;
    font-size: 12px;
}

.cyber-link {
    color: #ff00ff;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s;
    position: relative;
}

.cyber-link:hover {
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.cyber-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff00ff, #00ffff);
    transition: width 0.3s;
}

.cyber-link:hover::after {
    width: 100%;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container,
    .register-container,
    .forgot-container {
        padding: 30px 25px;
        width: 95%;
    }
    
    .form-title {
        font-size: 22px;
    }
    
    .neon-text {
        font-size: 40px;
    }
}
