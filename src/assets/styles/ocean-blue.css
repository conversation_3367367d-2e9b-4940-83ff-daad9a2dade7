* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #0077be;
    border-radius: 25px;
    color: #0077be;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 119, 190, 0.2);
}

.back-button:hover {
    background: #0077be;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 119, 190, 0.3);
}

/* 海洋波浪背景 */
.ocean-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(180deg, #87CEEB 0%, #4682B4 50%, #191970 100%);
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23ffffff'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23ffffff'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23ffffff'/%3E%3C/svg%3E") repeat-x;
    animation: waveMove 10s ease-in-out infinite;
}

.wave1 {
    animation-delay: 0s;
    opacity: 0.7;
}

.wave2 {
    animation-delay: -2s;
    opacity: 0.5;
    height: 80px;
}

.wave3 {
    animation-delay: -4s;
    opacity: 0.3;
    height: 60px;
}

@keyframes waveMove {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-50%);
    }
}

/* 气泡效果 */
.bubbles-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    animation: bubbleRise 8s linear infinite;
}

.bubble:nth-child(1) {
    width: 20px;
    height: 20px;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.bubble:nth-child(2) {
    width: 15px;
    height: 15px;
    left: 25%;
    animation-delay: 2s;
    animation-duration: 10s;
}

.bubble:nth-child(3) {
    width: 25px;
    height: 25px;
    left: 40%;
    animation-delay: 4s;
    animation-duration: 7s;
}

.bubble:nth-child(4) {
    width: 18px;
    height: 18px;
    left: 60%;
    animation-delay: 1s;
    animation-duration: 9s;
}

.bubble:nth-child(5) {
    width: 22px;
    height: 22px;
    left: 75%;
    animation-delay: 3s;
    animation-duration: 8.5s;
}

.bubble:nth-child(6) {
    width: 16px;
    height: 16px;
    left: 90%;
    animation-delay: 5s;
    animation-duration: 7.5s;
}

@keyframes bubbleRise {
    0% {
        bottom: -100px;
        opacity: 0;
        transform: translateX(0);
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100vh;
        opacity: 0;
        transform: translateX(100px);
    }
}

/* 水滴效果 */
.water-drops {
    position: absolute;
    width: 100%;
    height: 100%;
}

.drop {
    position: absolute;
    width: 8px;
    height: 12px;
    background: rgba(135, 206, 235, 0.8);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: dropFall 6s linear infinite;
}

.drop:nth-child(1) {
    left: 20%;
    animation-delay: 0s;
}

.drop:nth-child(2) {
    left: 50%;
    animation-delay: 1.5s;
}

.drop:nth-child(3) {
    left: 70%;
    animation-delay: 3s;
}

.drop:nth-child(4) {
    left: 85%;
    animation-delay: 4.5s;
}

@keyframes dropFall {
    0% {
        top: -20px;
        opacity: 1;
    }
    100% {
        top: 100vh;
        opacity: 0;
    }
}

/* 海洋卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.ocean-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 
        0 20px 40px rgba(0, 119, 190, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.3);
    animation: cardFloat 0.8s ease-out;
    border: 2px solid rgba(0, 119, 190, 0.1);
}

@keyframes cardFloat {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.ocean-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: oceanWave 3s ease-in-out infinite;
}

@keyframes oceanWave {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(-5deg) scale(1.05); }
    75% { transform: rotate(5deg) scale(1.05); }
}

.card-header h2 {
    color: #0077be;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px rgba(0, 119, 190, 0.3);
}

.subtitle {
    color: rgba(0, 119, 190, 0.7);
    font-size: 0.9rem;
    margin-bottom: 20px;
    font-weight: 300;
    font-style: italic;
    letter-spacing: 1px;
}

.wave-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #0077be, transparent);
    margin: 0 auto;
    border-radius: 2px;
    animation: waveDivider 2s ease-in-out infinite;
}

@keyframes waveDivider {
    0%, 100% { transform: scaleX(1); opacity: 0.7; }
    50% { transform: scaleX(1.2); opacity: 1; }
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(0, 119, 190, 0.3);
    color: #333;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: #0077be;
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 15px;
    color: rgba(0, 119, 190, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -5px;
    font-size: 12px;
    color: #0077be;
    font-weight: 500;
}

.water-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #87CEEB, #0077be, #4682B4);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.form-group input:focus ~ .water-line {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn, .ocean-btn-small {
    padding: 12px 20px;
    background: linear-gradient(135deg, #87CEEB, #0077be);
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(0, 119, 190, 0.3);
}

.verify-btn:hover:not(:disabled), .ocean-btn-small:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 119, 190, 0.4);
}

.verify-btn:disabled, .ocean-btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 登录按钮 */
.login-btn, .ocean-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #87CEEB, #0077be);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(0, 119, 190, 0.3);
}

.login-btn:hover, .ocean-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 119, 190, 0.4);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-wave {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover .btn-wave, .ocean-btn:hover .btn-wave {
    left: 100%;
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.ocean-link {
    color: #0077be;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.ocean-link:hover {
    color: #4682B4;
    text-shadow: 0 0 10px rgba(0, 119, 190, 0.5);
}

.divider {
    color: rgba(0, 119, 190, 0.5);
    margin: 0 15px;
    font-weight: bold;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .ocean-card {
        padding: 30px 20px;
        margin: 0 15px;
    }
    
    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .verification-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .verify-btn, .ocean-btn-small {
        width: 100%;
    }
    
    .ocean-icon {
        font-size: 2.5rem;
    }
}
