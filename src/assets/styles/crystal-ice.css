/* 水晶冰雪风格样式 */
:root {
    --ice-blue: #E0F6FF;
    --crystal-white: #F0F8FF;
    --frost-blue: #B0E0E6;
    --aurora-cyan: #00FFFF;
    --diamond-silver: #C0C0C0;
    --ice-glow: rgba(0, 255, 255, 0.6);
    --crystal-glow: rgba(240, 248, 255, 0.8);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(240, 248, 255, 0.9);
    border: 2px solid var(--aurora-cyan);
    border-radius: 25px;
    color: var(--aurora-cyan);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--ice-glow);
    backdrop-filter: blur(10px);
}

.back-button:hover {
    background: var(--aurora-cyan);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--ice-glow);
}

/* 水晶冰雪背景 */
.crystal-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(135deg, var(--crystal-white) 0%, var(--ice-blue) 50%, var(--frost-blue) 100%);
    animation: crystalShimmer 10s ease-in-out infinite;
}

@keyframes crystalShimmer {
    0%, 100% { 
        filter: brightness(1) hue-rotate(0deg);
    }
    25% { 
        filter: brightness(1.1) hue-rotate(5deg);
    }
    50% { 
        filter: brightness(0.95) hue-rotate(-3deg);
    }
    75% { 
        filter: brightness(1.05) hue-rotate(8deg);
    }
}

/* 极光背景 */
.aurora-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.7;
}

.aurora-wave {
    position: absolute;
    width: 200%;
    height: 100px;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(0, 255, 255, 0.3), 
        rgba(176, 224, 230, 0.4), 
        rgba(0, 255, 255, 0.3), 
        transparent);
    animation: auroraFlow 15s ease-in-out infinite;
    border-radius: 50%;
    filter: blur(2px);
}

.aurora-wave1 {
    top: 20%;
    animation-delay: 0s;
}

.aurora-wave2 {
    top: 50%;
    animation-delay: -5s;
    opacity: 0.6;
}

.aurora-wave3 {
    top: 80%;
    animation-delay: -10s;
    opacity: 0.4;
}

@keyframes auroraFlow {
    0%, 100% {
        transform: translateX(-50%) rotate(0deg);
    }
    33% {
        transform: translateX(-30%) rotate(2deg);
    }
    66% {
        transform: translateX(-70%) rotate(-2deg);
    }
}

/* 雪花粒子效果 */
.snowflakes-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.snowflake {
    position: absolute;
    color: var(--crystal-white);
    font-size: 16px;
    animation: snowfall 8s linear infinite;
    text-shadow: 0 0 5px var(--ice-glow);
}

.snowflake:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.snowflake:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 9s;
}

.snowflake:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.snowflake:nth-child(4) {
    left: 40%;
    animation-delay: 0.5s;
    animation-duration: 8.5s;
}

.snowflake:nth-child(5) {
    left: 50%;
    animation-delay: 1.5s;
    animation-duration: 7.5s;
}

.snowflake:nth-child(6) {
    left: 60%;
    animation-delay: 2.5s;
    animation-duration: 9.5s;
}

.snowflake:nth-child(7) {
    left: 70%;
    animation-delay: 3s;
    animation-duration: 8s;
}

.snowflake:nth-child(8) {
    left: 80%;
    animation-delay: 3.5s;
    animation-duration: 7s;
}

.snowflake:nth-child(9) {
    left: 90%;
    animation-delay: 4s;
    animation-duration: 8.5s;
}

.snowflake:nth-child(10) {
    left: 95%;
    animation-delay: 4.5s;
    animation-duration: 9s;
}

@keyframes snowfall {
    0% {
        top: -10px;
        opacity: 0;
        transform: translateX(0) rotate(0deg);
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100vh;
        opacity: 0;
        transform: translateX(100px) rotate(360deg);
    }
}

/* 水晶装饰 */
.crystal-decorations {
    position: absolute;
    width: 100%;
    height: 100%;
}

.crystal-orb {
    position: absolute;
    font-size: 30px;
    animation: crystalFloat 12s ease-in-out infinite;
    filter: drop-shadow(0 0 10px var(--ice-glow));
}

.crystal-orb1 {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.crystal-orb2 {
    top: 70%;
    right: 15%;
    animation-delay: 4s;
}

.crystal-orb3 {
    top: 40%;
    left: 80%;
    animation-delay: 8s;
}

@keyframes crystalFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    33% {
        transform: translateY(-20px) rotate(120deg) scale(1.1);
        opacity: 1;
    }
    66% {
        transform: translateY(10px) rotate(240deg) scale(0.9);
        opacity: 0.9;
    }
}

/* 冰晶闪烁效果 */
.ice-sparkles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.ice-sparkle {
    position: absolute;
    font-size: 12px;
    color: var(--aurora-cyan);
    animation: iceSparkle 3s ease-in-out infinite;
}

.ice-sparkle:nth-child(1) {
    top: 25%;
    left: 30%;
    animation-delay: 0s;
}

.ice-sparkle:nth-child(2) {
    top: 60%;
    left: 70%;
    animation-delay: 1s;
}

.ice-sparkle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 2s;
}

.ice-sparkle:nth-child(4) {
    top: 40%;
    right: 25%;
    animation-delay: 0.5s;
}

.ice-sparkle:nth-child(5) {
    top: 10%;
    right: 40%;
    animation-delay: 1.5s;
}

@keyframes iceSparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* 水晶卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.crystal-card {
    background: rgba(240, 248, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 
        0 20px 40px rgba(0, 255, 255, 0.2),
        0 0 0 1px rgba(176, 224, 230, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    animation: cardFloat 0.8s ease-out;
    border: 2px solid rgba(0, 255, 255, 0.3);
}

@keyframes cardFloat {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.crystal-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: crystalPulse 3s ease-in-out infinite;
    filter: drop-shadow(0 0 10px var(--ice-glow));
}

@keyframes crystalPulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 10px var(--ice-glow));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 20px var(--ice-glow));
    }
}

.card-header h2 {
    color: var(--aurora-cyan);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px var(--ice-glow);
}

.subtitle {
    color: rgba(0, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 20px;
    font-weight: 300;
    font-style: italic;
    letter-spacing: 1px;
}

.ice-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--aurora-cyan), var(--crystal-white), var(--aurora-cyan), transparent);
    margin: 0 auto;
    border-radius: 2px;
    animation: iceDivider 4s ease-in-out infinite;
}

@keyframes iceDivider {
    0%, 100% {
        transform: scaleX(1);
        opacity: 0.8;
        box-shadow: 0 0 5px var(--ice-glow);
    }
    50% {
        transform: scaleX(1.2);
        opacity: 1;
        box-shadow: 0 0 15px var(--ice-glow);
    }
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(0, 255, 255, 0.4);
    color: var(--aurora-cyan);
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: var(--aurora-cyan);
    box-shadow: 0 2px 10px var(--ice-glow);
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 15px;
    color: rgba(0, 255, 255, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -5px;
    font-size: 12px;
    color: var(--aurora-cyan);
    font-weight: 500;
    text-shadow: 0 0 5px var(--ice-glow);
}

.frost-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--aurora-cyan), var(--crystal-white), var(--aurora-cyan));
    transition: all 0.3s ease;
    transform: translateX(-50%);
    box-shadow: 0 0 5px var(--ice-glow);
}

.form-group input:focus ~ .frost-line {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn, .crystal-btn-small {
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--frost-blue), var(--aurora-cyan));
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px var(--ice-glow);
    border: 1px solid var(--crystal-white);
}

.verify-btn:hover:not(:disabled), .crystal-btn-small:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--ice-glow);
    background: linear-gradient(135deg, var(--aurora-cyan), var(--crystal-white));
}

.verify-btn:disabled, .crystal-btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 登录按钮 */
.login-btn, .crystal-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, var(--frost-blue), var(--aurora-cyan));
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
    box-shadow: 0 8px 25px var(--ice-glow);
    border: 2px solid var(--crystal-white);
}

.login-btn:hover, .crystal-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px var(--ice-glow);
    background: linear-gradient(135deg, var(--aurora-cyan), var(--crystal-white));
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-frost {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(240, 248, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover .btn-frost, .crystal-btn:hover .btn-frost {
    left: 100%;
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.crystal-link {
    color: var(--aurora-cyan);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.crystal-link:hover {
    color: var(--frost-blue);
    text-shadow: 0 0 10px var(--ice-glow);
}

.divider {
    color: rgba(0, 255, 255, 0.6);
    margin: 0 15px;
    font-weight: bold;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .crystal-card {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }

    .verification-group {
        flex-direction: column;
        gap: 15px;
    }

    .verify-btn, .crystal-btn-small {
        width: 100%;
    }

    .crystal-icon {
        font-size: 2.5rem;
    }
}
