* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    color: #ffffff;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 玻璃背景 */
.glass-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.glass-orb {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: glassFloat 8s ease-in-out infinite;
}

.orb-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.orb-3 {
    width: 120px;
    height: 120px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.orb-4 {
    width: 180px;
    height: 180px;
    top: 30%;
    right: 40%;
    animation-delay: 6s;
}

.glass-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    backdrop-filter: blur(5px);
    animation: particleFloat 6s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.particle:nth-child(1) { top: 20%; left: 15%; animation-delay: 0s; }
.particle:nth-child(2) { top: 70%; left: 80%; animation-delay: 1.2s; }
.particle:nth-child(3) { top: 40%; left: 60%; animation-delay: 2.4s; }
.particle:nth-child(4) { top: 80%; left: 30%; animation-delay: 3.6s; }
.particle:nth-child(5) { top: 15%; left: 70%; animation-delay: 4.8s; }

@keyframes glassFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
    50% { transform: translateY(-30px) scale(1.05); opacity: 1; }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
    50% { transform: translateY(-40px) scale(1.2); opacity: 1; }
}

/* 表单容器 */
.form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    transition: all 0.5s ease;
    opacity: 1;
    visibility: visible;
}

.form-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    pointer-events: none;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

.glass-card h2 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.95);
    letter-spacing: 1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 400;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-group input {
    width: 100%;
    padding: 16px 12px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    outline: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.form-group label {
    position: absolute;
    top: 16px;
    left: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -8px;
    left: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 0 4px;
    border-radius: 4px;
}

.glass-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid transparent;
    border-radius: 12px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1)) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-group input:focus ~ .glass-border {
    opacity: 1;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.verify-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 玻璃按钮 */
.glass-btn {
    width: 100%;
    padding: 16px;
    margin: 32px 0 24px 0;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
}

.glass-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: all 0.5s ease;
}

.glass-btn:hover .glass-shine {
    left: 100%;
}

/* 附加链接 */
.additional-links {
    text-align: center;
    font-size: 14px;
}

.additional-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.additional-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.additional-links a:hover::after {
    width: 100%;
}

.additional-links a:hover {
    color: rgba(255, 255, 255, 1);
}

.divider {
    margin: 0 12px;
    color: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .form-container {
        padding: 16px;
    }
    
    .glass-card {
        padding: 24px;
    }
    
    .glass-card h2 {
        font-size: 24px;
    }
    
    .back-button {
        top: 16px;
        left: 16px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .glass-orb {
        display: none;
    }
}

/* 加载动画 */
@keyframes glassFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        backdrop-filter: blur(30px);
    }
}

.glass-card {
    animation: glassFadeIn 1s ease-out;
}
