* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fa;
    color: #333;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: white;
    border: 2px solid #007bff;
    border-radius: 6px;
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
}

.back-button:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

/* 几何装饰背景 */
.background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.geometric-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    opacity: 0.1;
    animation: shapeFloat 8s ease-in-out infinite;
}

.shape.circle {
    width: 80px;
    height: 80px;
    border: 3px solid #007bff;
    border-radius: 50%;
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.shape.square {
    width: 60px;
    height: 60px;
    border: 3px solid #28a745;
    top: 60%;
    right: 20%;
    animation-delay: 1s;
    transform: rotate(45deg);
}

.shape.triangle {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 52px solid #ffc107;
    top: 30%;
    right: 30%;
    animation-delay: 2s;
}

.shape.hexagon {
    width: 50px;
    height: 28.87px;
    background: #17a2b8;
    position: relative;
    top: 70%;
    left: 25%;
    animation-delay: 3s;
}

.shape.hexagon:before,
.shape.hexagon:after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
}

.shape.hexagon:before {
    bottom: 100%;
    border-bottom: 14.43px solid #17a2b8;
}

.shape.hexagon:after {
    top: 100%;
    border-top: 14.43px solid #17a2b8;
}

.shape.diamond {
    width: 40px;
    height: 40px;
    background: #dc3545;
    transform: rotate(45deg);
    bottom: 20%;
    right: 15%;
    animation-delay: 4s;
}

@keyframes shapeFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

.grid-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 123, 255, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 123, 255, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
    animation: gridSlide 30s linear infinite;
}

@keyframes gridSlide {
    0% { transform: translate(0, 0); }
    100% { transform: translate(40px, 40px); }
}

.brand-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.brand-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, #007bff, transparent);
    opacity: 0.2;
}

.brand-line.horizontal {
    width: 200px;
    height: 2px;
    top: 25%;
    left: 10%;
    animation: lineMove 6s ease-in-out infinite;
}

.brand-line.vertical {
    width: 2px;
    height: 150px;
    top: 50%;
    right: 15%;
    animation: lineMove 6s ease-in-out infinite 2s;
}

@keyframes lineMove {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.4; }
}

/* 商务卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 420px;
    margin: 0 20px;
}

.business-card {
    background: white;
    border-radius: 12px;
    padding: 40px 35px;
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.1),
        0 1px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    animation: cardSlideIn 0.8s ease-out;
}

@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-header {
    text-align: center;
    margin-bottom: 35px;
}

.company-logo {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #007bff;
    animation: logoScale 2s ease-in-out infinite;
}

@keyframes logoScale {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.card-header h2 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.subtitle {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 20px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.header-divider {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    margin: 0 auto;
    border-radius: 2px;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 28px;
}

.form-group input {
    width: 100%;
    padding: 16px 0 8px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid #e9ecef;
    color: #333;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-group input:focus {
    border-bottom-color: #007bff;
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 16px;
    color: #6c757d;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: 0;
    font-size: 12px;
    color: #007bff;
    font-weight: 600;
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #007bff;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.form-group input:focus ~ .input-line {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn, .business-btn-small {
    padding: 12px 24px;
    background: #007bff;
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.verify-btn:hover:not(:disabled), .business-btn-small:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.verify-btn:disabled, .business-btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 登录按钮 */
.login-btn, .business-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 25px 0;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover, .business-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-arrow {
    font-size: 18px;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-arrow, .business-btn:hover .btn-arrow {
    transform: translateX(5px);
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 25px;
}

.business-link {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.business-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.divider {
    color: #6c757d;
    margin: 0 15px;
    font-weight: 300;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .business-card {
        padding: 30px 25px;
        margin: 0 15px;
    }
    
    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .verification-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .verify-btn, .business-btn-small {
        width: 100%;
    }
    
    .company-logo {
        font-size: 2.5rem;
    }
    
    .card-header h2 {
        font-size: 1.6rem;
    }
}
