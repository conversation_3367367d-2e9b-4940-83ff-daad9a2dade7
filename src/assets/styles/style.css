* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f0f2f5;
    position: relative;
    overflow: hidden;
}

.background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(45deg, #6ac1c5, #bda5ff);
}

.bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 8s ease-in-out infinite;
}

.bubble:nth-child(1) {
    width: 80px;
    height: 80px;
    left: 10%;
    animation-delay: 0s;
}

.bubble:nth-child(2) {
    width: 100px;
    height: 100px;
    right: 15%;
    animation-delay: 2s;
}

.bubble:nth-child(3) {
    width: 60px;
    height: 60px;
    left: 35%;
    top: 70%;
    animation-delay: 4s;
}

.bubble:nth-child(4) {
    width: 120px;
    height: 120px;
    right: 30%;
    top: 40%;
    animation-delay: 6s;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
    100% {
        transform: translateY(0) rotate(360deg);
    }
}

.login-container {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.18);
    width: 90%;
    max-width: 400px;
    position: relative;
    z-index: 1;
}

h2 {
    text-align: center;
    color: #fff;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s;
    background: rgba(255, 255, 255, 0.1);
    color: #333;
}

label {
    position: absolute;
    left: 0.8rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s;
    pointer-events: none;
}

input:focus,
input:valid {
    border-color: rgba(106, 193, 197, 0.8);
    background: rgba(255, 255, 255, 0.2);
}

input:focus + label,
input:valid + label {
    top: -0.5rem;
    left: 0.5rem;
    font-size: 0.8rem;
    background: rgba(255, 255, 255, 0.8);
    padding: 0 0.3rem;
    color: #6ac1c5;
}

.login-btn {
    width: 100%;
    padding: 0.8rem;
    background: linear-gradient(45deg, #6ac1c5, #bda5ff);
    border: none;
    border-radius: 0.5rem;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.login-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.login-btn:active {
    transform: translateY(1px);
}

.additional-links {
    margin-top: 1.5rem;
    text-align: center;
    font-size: 0.9rem;
}

.additional-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s;
}

.additional-links a.register {
    color: #9364e7;
    font-weight: 500;
}

.additional-links a:hover {
    color: #fff;
}

.additional-links a.register:hover {
    color: #5db1b5;
}

.divider {
    margin: 0 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

@media (max-width: 320px) {
    .login-container {
        padding: 1.5rem;
    }
    
    h2 {
        font-size: 1.3rem;
    }
    
    input {
        font-size: 0.9rem;
    }
}

.hidden {
    display: none;
}

.form-container {
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
}

.form-container.fade-out {
    opacity: 0;
}

.form-footer {
    margin-top: 20px;
    text-align: center;
}

.form-footer a {
    color: #666;
    text-decoration: none;
    margin: 0 10px;
    font-size: 14px;
}

.form-footer a:hover {
    color: #1a73e8;
}

.verification-group {
    position: relative;
}

.verify-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(106, 193, 197, 0.8);
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s;
}

.verify-btn:hover {
    background: rgba(106, 193, 197, 1);
}

.verify-btn:disabled {
    background: rgba(106, 193, 197, 0.4);
    cursor: not-allowed;
}

#verificationCode {
    padding-right: 90px;
} 