/* 樱花和风风格样式 */
:root {
    --sakura-pink: #FFB7C5;
    --zen-beige: #F5F5DC;
    --bamboo-green: #9ACD32;
    --ink-black: #2F4F4F;
    --paper-white: #FFFAF0;
    --zen-glow: rgba(255, 183, 197, 0.6);
    --ink-glow: rgba(47, 79, 79, 0.5);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Hiragino Sans", "Yu Gothic", "Meiryo", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 250, 240, 0.95);
    border: 2px solid var(--sakura-pink);
    border-radius: 25px;
    color: var(--ink-black);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--zen-glow);
    backdrop-filter: blur(10px);
}

.back-button:hover {
    background: var(--sakura-pink);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--zen-glow);
}

/* 樱花和风背景 */
.sakura-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(135deg, var(--paper-white) 0%, var(--zen-beige) 50%, #F0E68C 100%);
    animation: zenBreathing 12s ease-in-out infinite;
}

@keyframes zenBreathing {
    0%, 100% { 
        filter: brightness(1) saturate(1);
    }
    25% { 
        filter: brightness(1.05) saturate(1.1);
    }
    50% { 
        filter: brightness(0.98) saturate(0.95);
    }
    75% { 
        filter: brightness(1.02) saturate(1.05);
    }
}

/* 水墨画背景 */
.ink-wash-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.ink-cloud {
    position: absolute;
    width: 300px;
    height: 200px;
    background: radial-gradient(ellipse, rgba(47, 79, 79, 0.2), transparent);
    border-radius: 50%;
    animation: inkFlow 20s ease-in-out infinite;
    filter: blur(3px);
}

.ink-cloud1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.ink-cloud2 {
    top: 60%;
    right: 15%;
    animation-delay: -7s;
    width: 250px;
    height: 150px;
}

.ink-cloud3 {
    bottom: 20%;
    left: 50%;
    animation-delay: -14s;
    width: 200px;
    height: 120px;
}

@keyframes inkFlow {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1);
        opacity: 0.2;
    }
    33% {
        transform: translateX(20px) translateY(-10px) scale(1.1);
        opacity: 0.3;
    }
    66% {
        transform: translateX(-15px) translateY(15px) scale(0.9);
        opacity: 0.25;
    }
}

/* 樱花花瓣效果 */
.sakura-petals-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.sakura-petal {
    position: absolute;
    font-size: 18px;
    animation: petalFall 10s linear infinite;
    color: var(--sakura-pink);
    text-shadow: 0 0 5px var(--zen-glow);
}

.sakura-petal:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 10s;
}

.sakura-petal:nth-child(2) {
    left: 25%;
    animation-delay: 2s;
    animation-duration: 12s;
}

.sakura-petal:nth-child(3) {
    left: 40%;
    animation-delay: 4s;
    animation-duration: 9s;
}

.sakura-petal:nth-child(4) {
    left: 55%;
    animation-delay: 1s;
    animation-duration: 11s;
}

.sakura-petal:nth-child(5) {
    left: 70%;
    animation-delay: 3s;
    animation-duration: 10.5s;
}

.sakura-petal:nth-child(6) {
    left: 85%;
    animation-delay: 5s;
    animation-duration: 9.5s;
}

.sakura-petal:nth-child(7) {
    left: 15%;
    animation-delay: 6s;
    animation-duration: 11.5s;
}

.sakura-petal:nth-child(8) {
    left: 90%;
    animation-delay: 7s;
    animation-duration: 8.5s;
}

@keyframes petalFall {
    0% {
        top: -20px;
        opacity: 0;
        transform: translateX(0) rotate(0deg);
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        top: 100vh;
        opacity: 0;
        transform: translateX(50px) rotate(360deg);
    }
}

/* 竹林装饰 */
.bamboo-decorations {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bamboo-stalk {
    position: absolute;
    font-size: 40px;
    color: var(--bamboo-green);
    animation: bambooSway 8s ease-in-out infinite;
    filter: drop-shadow(0 0 5px rgba(154, 205, 50, 0.5));
}

.bamboo-stalk1 {
    bottom: 0;
    left: 5%;
    animation-delay: 0s;
}

.bamboo-stalk2 {
    bottom: 0;
    right: 8%;
    animation-delay: 2s;
}

.bamboo-leaf {
    position: absolute;
    font-size: 16px;
    color: var(--bamboo-green);
    animation: leafDance 6s ease-in-out infinite;
}

.bamboo-leaf:nth-child(3) {
    top: 30%;
    left: 8%;
    animation-delay: 1s;
}

.bamboo-leaf:nth-child(4) {
    top: 25%;
    right: 12%;
    animation-delay: 3s;
}

@keyframes bambooSway {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(2deg);
    }
    75% {
        transform: rotate(-2deg);
    }
}

@keyframes leafDance {
    0%, 100% {
        transform: translateX(0) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: translateX(10px) rotate(15deg);
        opacity: 1;
    }
}

/* 纸灯笼 */
.paper-lanterns {
    position: absolute;
    width: 100%;
    height: 100%;
}

.paper-lantern {
    position: absolute;
    font-size: 25px;
    animation: lanternGlow 4s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(255, 183, 197, 0.8));
}

.lantern1 {
    top: 15%;
    right: 20%;
    animation-delay: 0s;
}

.lantern2 {
    top: 70%;
    left: 15%;
    animation-delay: 2s;
}

@keyframes lanternGlow {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 10px rgba(255, 183, 197, 0.8));
    }
    50% {
        transform: scale(1.1);
        filter: drop-shadow(0 0 20px rgba(255, 183, 197, 1));
    }
}

/* 蝴蝶飞舞 */
.butterflies {
    position: absolute;
    width: 100%;
    height: 100%;
}

.butterfly {
    position: absolute;
    font-size: 20px;
    animation: butterflyFly 15s ease-in-out infinite;
    color: var(--sakura-pink);
}

.butterfly:nth-child(1) {
    top: 20%;
    left: 30%;
    animation-delay: 0s;
}

.butterfly:nth-child(2) {
    top: 50%;
    right: 25%;
    animation-delay: 5s;
}

.butterfly:nth-child(3) {
    top: 80%;
    left: 60%;
    animation-delay: 10s;
}

@keyframes butterflyFly {
    0%, 100% {
        transform: translateX(0) translateY(0) rotate(0deg);
    }
    25% {
        transform: translateX(50px) translateY(-30px) rotate(10deg);
    }
    50% {
        transform: translateX(100px) translateY(20px) rotate(-5deg);
    }
    75% {
        transform: translateX(30px) translateY(-10px) rotate(15deg);
    }
}

/* 和风卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.zen-card {
    background: rgba(255, 250, 240, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 
        0 20px 40px rgba(255, 183, 197, 0.2),
        0 0 0 1px rgba(154, 205, 50, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    animation: cardFloat 0.8s ease-out;
    border: 2px solid rgba(255, 183, 197, 0.3);
}

@keyframes cardFloat {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.zen-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: zenPulse 3s ease-in-out infinite;
    filter: drop-shadow(0 0 10px var(--zen-glow));
}

@keyframes zenPulse {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 0 10px var(--zen-glow));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 0 15px var(--zen-glow));
    }
}

.card-header h2 {
    color: var(--ink-black);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px var(--zen-glow);
}

.subtitle {
    color: rgba(47, 79, 79, 0.8);
    font-size: 0.9rem;
    margin-bottom: 20px;
    font-weight: 300;
    font-style: italic;
    letter-spacing: 1px;
}

.zen-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--sakura-pink), var(--bamboo-green), var(--sakura-pink), transparent);
    margin: 0 auto;
    border-radius: 2px;
    animation: zenDivider 5s ease-in-out infinite;
}

@keyframes zenDivider {
    0%, 100% {
        transform: scaleX(1);
        opacity: 0.8;
        box-shadow: 0 0 5px var(--zen-glow);
    }
    50% {
        transform: scaleX(1.2);
        opacity: 1;
        box-shadow: 0 0 15px var(--zen-glow);
    }
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(255, 183, 197, 0.4);
    color: var(--ink-black);
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: var(--sakura-pink);
    box-shadow: 0 2px 10px var(--zen-glow);
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 15px;
    color: rgba(47, 79, 79, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -5px;
    font-size: 12px;
    color: var(--ink-black);
    font-weight: 500;
    text-shadow: 0 0 5px var(--zen-glow);
}

.ink-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--sakura-pink), var(--bamboo-green), var(--sakura-pink));
    transition: all 0.3s ease;
    transform: translateX(-50%);
    box-shadow: 0 0 5px var(--zen-glow);
}

.form-group input:focus ~ .ink-line {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn, .zen-btn-small {
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--sakura-pink), var(--bamboo-green));
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px var(--zen-glow);
    border: 1px solid var(--paper-white);
}

.verify-btn:hover:not(:disabled), .zen-btn-small:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--zen-glow);
    background: linear-gradient(135deg, var(--bamboo-green), var(--sakura-pink));
}

.verify-btn:disabled, .zen-btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 登录按钮 */
.login-btn, .zen-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, var(--sakura-pink), var(--bamboo-green));
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
    box-shadow: 0 8px 25px var(--zen-glow);
    border: 2px solid var(--paper-white);
}

.login-btn:hover, .zen-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px var(--zen-glow);
    background: linear-gradient(135deg, var(--bamboo-green), var(--sakura-pink));
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-ink {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 250, 240, 0.4), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover .btn-ink, .zen-btn:hover .btn-ink {
    left: 100%;
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.zen-link {
    color: var(--ink-black);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.zen-link:hover {
    color: var(--sakura-pink);
    text-shadow: 0 0 10px var(--zen-glow);
}

.divider {
    color: rgba(255, 183, 197, 0.8);
    margin: 0 15px;
    font-weight: bold;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .zen-card {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }

    .verification-group {
        flex-direction: column;
        gap: 15px;
    }

    .verify-btn, .zen-btn-small {
        width: 100%;
    }

    .zen-icon {
        font-size: 2.5rem;
    }
}
