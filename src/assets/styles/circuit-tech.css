* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Courier New', monospace;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: #0a0f0a;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(0, 255, 100, 0.1);
    border: 2px solid #00ff64;
    border-radius: 8px;
    color: #00ff64;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(0, 255, 100, 0.3);
}

.back-button:hover {
    background: rgba(0, 255, 100, 0.2);
    box-shadow: 0 0 30px rgba(0, 255, 100, 0.5);
    transform: translateY(-2px);
}

/* 电路板背景 */
.circuit-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(0, 255, 100, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(0, 200, 255, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0f0a 0%, #0d1a0d 50%, #0a0f0a 100%);
    z-index: -1;
}

.circuit-board {
    position: absolute;
    width: 100%;
    height: 100%;
}

.circuit-line {
    position: absolute;
    background: linear-gradient(90deg, transparent 0%, #00ff64 50%, transparent 100%);
    opacity: 0.6;
}

.circuit-line.horizontal {
    height: 2px;
    width: 100%;
    animation: circuitFlow 4s linear infinite;
}

.circuit-line.vertical {
    width: 2px;
    height: 100%;
    background: linear-gradient(0deg, transparent 0%, #00ff64 50%, transparent 100%);
    animation: circuitFlowVertical 5s linear infinite;
}

.line-1 { top: 20%; animation-delay: 0s; }
.line-2 { top: 50%; animation-delay: 1s; }
.line-3 { top: 80%; animation-delay: 2s; }
.line-4 { left: 15%; animation-delay: 0.5s; }
.line-5 { left: 50%; animation-delay: 1.5s; }
.line-6 { left: 85%; animation-delay: 2.5s; }

@keyframes circuitFlow {
    0% { background-position: -100% 0; }
    100% { background-position: 200% 0; }
}

@keyframes circuitFlowVertical {
    0% { background-position: 0 -100%; }
    100% { background-position: 0 200%; }
}

/* 电子元件 */
.electronic-components {
    position: absolute;
    width: 100%;
    height: 100%;
}

.component {
    position: absolute;
    font-size: 20px;
    animation: componentGlow 2s ease-in-out infinite alternate;
}

.chip {
    color: #00c8ff;
    filter: drop-shadow(0 0 10px #00c8ff);
}

.led {
    color: #ff6b00;
    filter: drop-shadow(0 0 15px #ff6b00);
    animation: ledBlink 1.5s ease-in-out infinite;
}

.resistor {
    color: #ffff00;
    filter: drop-shadow(0 0 8px #ffff00);
    font-family: monospace;
}

.chip-1 { top: 15%; left: 10%; animation-delay: 0s; }
.chip-2 { top: 70%; right: 15%; animation-delay: 1s; }
.led-1 { top: 25%; right: 20%; animation-delay: 0.3s; }
.led-2 { bottom: 30%; left: 25%; animation-delay: 0.8s; }
.led-3 { top: 60%; left: 70%; animation-delay: 1.2s; }
.resistor-1 { top: 40%; left: 5%; animation-delay: 0.5s; }
.resistor-2 { bottom: 20%; right: 10%; animation-delay: 1.5s; }

@keyframes componentGlow {
    0% { opacity: 0.6; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.1); }
}

@keyframes ledBlink {
    0%, 50% { opacity: 0.3; }
    51%, 100% { opacity: 1; }
}

/* 数据流 */
.data-flow {
    position: absolute;
    width: 100%;
    height: 100%;
}

.data-packet {
    position: absolute;
    color: #00ff64;
    font-size: 12px;
    filter: drop-shadow(0 0 10px #00ff64);
    animation: dataMove 6s linear infinite;
}

.packet-1 {
    top: 20%;
    left: -20px;
    animation-delay: 0s;
}

.packet-2 {
    top: 50%;
    left: -20px;
    animation-delay: 2s;
}

.packet-3 {
    top: 80%;
    left: -20px;
    animation-delay: 4s;
}

@keyframes dataMove {
    0% { left: -20px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: calc(100% + 20px); opacity: 0; }
}

/* 表单容器 */
.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    z-index: 100;
}

.form-container.hidden {
    display: none;
}

/* 科技卡片 */
.tech-card {
    background: rgba(10, 15, 10, 0.95);
    border: 2px solid #00ff64;
    border-radius: 12px;
    padding: 40px 30px;
    box-shadow: 
        0 0 30px rgba(0, 255, 100, 0.3),
        inset 0 0 20px rgba(0, 255, 100, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.tech-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff64, transparent);
    animation: scanLine 3s linear infinite;
}

@keyframes scanLine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #00ff64;
    box-shadow: 0 0 15px #00ff64;
    animation: statusPulse 2s ease-in-out infinite;
}

.status-indicator.register-mode {
    background: #00c8ff;
    box-shadow: 0 0 15px #00c8ff;
}

.status-indicator.reset-mode {
    background: #ff6b00;
    box-shadow: 0 0 15px #ff6b00;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.tech-lines {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.tech-line {
    width: 30px;
    height: 2px;
    background: #00ff64;
    opacity: 0.7;
}

.tech-card h2 {
    color: #00ff64;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    margin: 0;
    text-shadow: 0 0 10px rgba(0, 255, 100, 0.5);
}

.subtitle {
    color: #00c8ff;
    text-align: center;
    margin-bottom: 30px;
    font-size: 14px;
    opacity: 0.8;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(0, 255, 100, 0.3);
    color: #ffffff;
    font-size: 16px;
    font-family: 'Courier New', monospace;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: #00ff64;
    box-shadow: 0 2px 10px rgba(0, 255, 100, 0.3);
}

.form-group label {
    position: absolute;
    top: 15px;
    left: 0;
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -10px;
    font-size: 12px;
    color: #00ff64;
}

.input-circuit {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #00ff64;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px #00ff64;
}

.form-group input:focus ~ .input-circuit {
    width: 100%;
}

/* 按钮样式 */
.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, rgba(0, 255, 100, 0.1), rgba(0, 200, 255, 0.1));
    border: 2px solid #00ff64;
    border-radius: 8px;
    color: #00ff64;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
}

.login-btn:hover {
    background: linear-gradient(45deg, rgba(0, 255, 100, 0.2), rgba(0, 200, 255, 0.2));
    box-shadow: 0 0 25px rgba(0, 255, 100, 0.4);
    transform: translateY(-2px);
}

.btn-circuit {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 100, 0.3), transparent);
    transition: left 0.5s ease;
}

.login-btn:hover .btn-circuit {
    left: 100%;
}

.circuit-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 4px;
    background: #00ff64;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 255, 100, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 255, 100, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 255, 100, 0); }
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 12px 16px;
    background: rgba(0, 200, 255, 0.1);
    border: 1px solid #00c8ff;
    border-radius: 4px;
    color: #00c8ff;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
    white-space: nowrap;
    flex-shrink: 0;
}

.verify-btn:hover {
    background: rgba(0, 200, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 200, 255, 0.3);
    transform: translateY(-2px);
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.tech-link {
    color: #00c8ff;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-family: 'Courier New', monospace;
}

.tech-link:hover {
    color: #00ff64;
    text-shadow: 0 0 10px rgba(0, 255, 100, 0.5);
}

.divider {
    color: rgba(255, 255, 255, 0.3);
    margin: 0 15px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 15px;
    }
    
    .tech-card {
        padding: 30px 20px;
    }
    
    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 15px;
        font-size: 12px;
    }

    .verification-group {
        gap: 8px;
    }

    .verify-btn {
        padding: 10px 12px;
        font-size: 11px;
        min-width: 70px;
    }
}
