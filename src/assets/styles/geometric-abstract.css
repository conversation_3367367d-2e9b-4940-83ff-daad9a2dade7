* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    color: #333333;
    overflow: hidden;
}

.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: #667eea;
    border: 2px solid #764ba2;
    border-radius: 0;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
}

.back-button:hover {
    background: #764ba2;
    transform: translateY(-2px);
}

.geometric-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.geometric-shape {
    position: absolute;
    animation: geometricFloat 6s ease-in-out infinite;
}

.triangle-1 {
    width: 0;
    height: 0;
    border-left: 50px solid transparent;
    border-right: 50px solid transparent;
    border-bottom: 87px solid rgba(102, 126, 234, 0.3);
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.triangle-2 {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 52px solid rgba(245, 87, 108, 0.3);
    top: 70%;
    right: 20%;
    animation-delay: 2s;
}

.circle-1 {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(240, 147, 251, 0.3);
    top: 25%;
    right: 15%;
    animation-delay: 1s;
}

.circle-2 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(79, 172, 254, 0.3);
    bottom: 30%;
    left: 25%;
    animation-delay: 3s;
}

.square-1 {
    width: 70px;
    height: 70px;
    background: rgba(118, 75, 162, 0.3);
    top: 60%;
    left: 15%;
    animation-delay: 4s;
    transform: rotate(45deg);
}

.square-2 {
    width: 50px;
    height: 50px;
    background: rgba(102, 126, 234, 0.3);
    top: 40%;
    left: 60%;
    animation-delay: 5s;
    transform: rotate(30deg);
}

.geometric-lines .line {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    animation: lineMove 8s ease-in-out infinite;
}

.line-1 {
    width: 200px;
    height: 2px;
    top: 30%;
    left: 40%;
    transform: rotate(45deg);
    animation-delay: 0s;
}

.line-2 {
    width: 150px;
    height: 2px;
    top: 60%;
    right: 30%;
    transform: rotate(-30deg);
    animation-delay: 2s;
}

.line-3 {
    width: 100px;
    height: 2px;
    bottom: 25%;
    left: 50%;
    transform: rotate(60deg);
    animation-delay: 4s;
}

@keyframes geometricFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

@keyframes lineMove {
    0%, 100% { opacity: 0.2; transform: translateX(0px); }
    50% { opacity: 0.6; transform: translateX(20px); }
}

.form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    transition: all 0.5s ease;
    opacity: 1;
    visibility: visible;
}

.form-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.geometric-card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
}

.geometric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(240, 147, 251, 0.1) 100%);
    pointer-events: none;
}

.geometric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.geometric-card h2 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: bold;
    color: #667eea;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: #764ba2;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-group input {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: none;
    background: rgba(102, 126, 234, 0.05);
    font-size: 16px;
    color: #333333;
    outline: none;
    transition: all 0.3s ease;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    border: 2px solid rgba(102, 126, 234, 0.2);
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
}

.form-group input:focus {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
}

.form-group label {
    position: absolute;
    top: 16px;
    left: 12px;
    color: #667eea;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    font-weight: 500;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -8px;
    left: 8px;
    font-size: 12px;
    color: #764ba2;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 4px;
    font-weight: bold;
}

.geometric-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid transparent;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: all 0.3s ease;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
    pointer-events: none;
}

.form-group input:focus ~ .geometric-border {
    opacity: 1;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 16px 20px;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px));
}

.verify-btn:hover {
    background: linear-gradient(45deg, #f5576c, #4facfe);
    transform: translateY(-2px);
}

.geometric-btn {
    width: 100%;
    padding: 16px;
    margin: 32px 0 24px 0;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    text-transform: uppercase;
    letter-spacing: 2px;
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px));
}

.geometric-btn:hover {
    background: linear-gradient(45deg, #764ba2, #f093fb);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.geometric-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: all 0.5s ease;
}

.geometric-btn:hover .geometric-effect {
    left: 100%;
}

.additional-links {
    text-align: center;
    font-size: 14px;
}

.additional-links a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: bold;
}

.additional-links a:hover {
    color: #764ba2;
}

.divider {
    margin: 0 12px;
    color: #f093fb;
}

@media (max-width: 480px) {
    .form-container {
        padding: 16px;
    }
    
    .geometric-card {
        padding: 24px;
    }
    
    .geometric-card h2 {
        font-size: 24px;
    }
    
    .back-button {
        top: 16px;
        left: 16px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .geometric-shape, .geometric-lines {
        display: none;
    }
}

@keyframes geometricFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
        clip-path: polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 20px 100%, 0 calc(100% - 20px));
    }
}

.geometric-card {
    animation: geometricFadeIn 1s ease-out;
}
