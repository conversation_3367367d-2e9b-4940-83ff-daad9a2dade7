/* 火焰熔岩风格样式 */
:root {
    --primary-dark: #8B0000;
    --primary-medium: #DC143C;
    --primary-light: #FF4500;
    --accent-gold: #FFD700;
    --accent-orange: #FF8C00;
    --flame-glow: rgba(255, 69, 0, 0.8);
    --lava-glow: rgba(220, 20, 60, 0.6);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--primary-light);
    border-radius: 25px;
    color: var(--accent-gold);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--flame-glow);
}

.back-button:hover {
    background: var(--primary-light);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--flame-glow);
}

/* 火焰熔岩背景 */
.flame-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-medium) 30%, var(--primary-light) 70%, var(--accent-gold) 100%);
    animation: flameGradient 8s ease-in-out infinite;
}

@keyframes flameGradient {
    0%, 100% { filter: hue-rotate(0deg) brightness(1); }
    25% { filter: hue-rotate(10deg) brightness(1.1); }
    50% { filter: hue-rotate(-5deg) brightness(0.9); }
    75% { filter: hue-rotate(15deg) brightness(1.05); }
}

/* 火焰粒子效果 */
.flame-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.flame-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, var(--accent-gold), var(--primary-light));
    border-radius: 50%;
    animation: flameRise 6s linear infinite;
    box-shadow: 0 0 10px var(--flame-glow);
}

.flame-particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 6s;
}

.flame-particle:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 7s;
}

.flame-particle:nth-child(3) {
    left: 35%;
    animation-delay: 2s;
    animation-duration: 5s;
}

.flame-particle:nth-child(4) {
    left: 50%;
    animation-delay: 0.5s;
    animation-duration: 6.5s;
}

.flame-particle:nth-child(5) {
    left: 65%;
    animation-delay: 1.5s;
    animation-duration: 5.5s;
}

.flame-particle:nth-child(6) {
    left: 75%;
    animation-delay: 2.5s;
    animation-duration: 7.5s;
}

.flame-particle:nth-child(7) {
    left: 85%;
    animation-delay: 3s;
    animation-duration: 6s;
}

.flame-particle:nth-child(8) {
    left: 95%;
    animation-delay: 3.5s;
    animation-duration: 8s;
}

@keyframes flameRise {
    0% {
        bottom: -10px;
        opacity: 0;
        transform: translateX(0) scale(0.5);
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100vh;
        opacity: 0;
        transform: translateX(50px) scale(1.5);
    }
}

/* 熔岩流动效果 */
.lava-flow {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 120px;
}

.lava-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 80px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.8' fill='%23DC143C'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.6' fill='%23FF4500'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23FFD700'/%3E%3C/svg%3E") repeat-x;
    animation: lavaFlow 12s ease-in-out infinite;
}

.lava-wave1 {
    animation-delay: 0s;
    opacity: 0.8;
}

.lava-wave2 {
    animation-delay: -3s;
    opacity: 0.6;
    height: 60px;
}

.lava-wave3 {
    animation-delay: -6s;
    opacity: 0.4;
    height: 40px;
}

@keyframes lavaFlow {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-50%);
    }
}

/* 发光熔岩球 */
.lava-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
}

.lava-orb {
    position: absolute;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, var(--accent-gold), var(--primary-medium));
    border-radius: 50%;
    animation: lavaOrb 10s ease-in-out infinite;
    box-shadow: 0 0 30px var(--lava-glow);
}

.lava-orb:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.lava-orb:nth-child(2) {
    top: 60%;
    right: 20%;
    animation-delay: 3s;
}

.lava-orb:nth-child(3) {
    top: 40%;
    left: 70%;
    animation-delay: 6s;
}

@keyframes lavaOrb {
    0%, 100% {
        transform: scale(1) translateY(0);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.3) translateY(-20px);
        opacity: 1;
    }
}

/* 火花效果 */
.sparks-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.spark {
    position: absolute;
    width: 3px;
    height: 8px;
    background: linear-gradient(to top, var(--accent-gold), var(--primary-light));
    border-radius: 50%;
    animation: sparkFly 4s linear infinite;
}

.spark:nth-child(1) {
    left: 25%;
    animation-delay: 0s;
}

.spark:nth-child(2) {
    left: 45%;
    animation-delay: 1s;
}

.spark:nth-child(3) {
    left: 65%;
    animation-delay: 2s;
}

.spark:nth-child(4) {
    left: 80%;
    animation-delay: 3s;
}

.spark:nth-child(5) {
    left: 90%;
    animation-delay: 0.5s;
}

@keyframes sparkFly {
    0% {
        bottom: 10%;
        opacity: 1;
        transform: translateX(0) rotate(0deg);
    }
    100% {
        bottom: 90%;
        opacity: 0;
        transform: translateX(100px) rotate(360deg);
    }
}

/* 火焰卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.flame-card {
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 
        0 20px 40px rgba(220, 20, 60, 0.3),
        0 0 0 1px rgba(255, 69, 0, 0.3),
        inset 0 1px 0 rgba(255, 215, 0, 0.2);
    animation: cardFloat 0.8s ease-out;
    border: 2px solid rgba(255, 69, 0, 0.4);
}

@keyframes cardFloat {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.flame-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    animation: flameFlicker 2s ease-in-out infinite;
    filter: drop-shadow(0 0 10px var(--flame-glow));
}

@keyframes flameFlicker {
    0%, 100% { 
        transform: rotate(0deg) scale(1);
        filter: drop-shadow(0 0 10px var(--flame-glow));
    }
    25% { 
        transform: rotate(-3deg) scale(1.05);
        filter: drop-shadow(0 0 15px var(--flame-glow));
    }
    75% { 
        transform: rotate(3deg) scale(1.05);
        filter: drop-shadow(0 0 15px var(--flame-glow));
    }
}

.card-header h2 {
    color: var(--accent-gold);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px var(--flame-glow);
}

.subtitle {
    color: rgba(255, 140, 0, 0.8);
    font-size: 0.9rem;
    margin-bottom: 20px;
    font-weight: 300;
    font-style: italic;
    letter-spacing: 1px;
}

.flame-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--primary-light), var(--accent-gold), var(--primary-light), transparent);
    margin: 0 auto;
    border-radius: 2px;
    animation: flameDivider 3s ease-in-out infinite;
}

@keyframes flameDivider {
    0%, 100% {
        transform: scaleX(1);
        opacity: 0.8;
        box-shadow: 0 0 5px var(--flame-glow);
    }
    50% {
        transform: scaleX(1.3);
        opacity: 1;
        box-shadow: 0 0 15px var(--flame-glow);
    }
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(255, 69, 0, 0.4);
    color: var(--accent-gold);
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: var(--primary-light);
    box-shadow: 0 2px 10px var(--flame-glow);
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 15px;
    color: rgba(255, 140, 0, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -5px;
    font-size: 12px;
    color: var(--accent-gold);
    font-weight: 500;
    text-shadow: 0 0 5px var(--flame-glow);
}

.flame-line {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-light), var(--accent-gold), var(--primary-light));
    transition: all 0.3s ease;
    transform: translateX(-50%);
    box-shadow: 0 0 5px var(--flame-glow);
}

.form-group input:focus ~ .flame-line {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn, .flame-btn-small {
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-light));
    border: none;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px var(--flame-glow);
    border: 1px solid var(--accent-orange);
}

.verify-btn:hover:not(:disabled), .flame-btn-small:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--flame-glow);
    background: linear-gradient(135deg, var(--primary-light), var(--accent-gold));
}

.verify-btn:disabled, .flame-btn-small:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 登录按钮 */
.login-btn, .flame-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-light));
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
    box-shadow: 0 8px 25px var(--flame-glow);
    border: 2px solid var(--accent-orange);
}

.login-btn:hover, .flame-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px var(--flame-glow);
    background: linear-gradient(135deg, var(--primary-light), var(--accent-gold));
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-flame {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.4), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover .btn-flame, .flame-btn:hover .btn-flame {
    left: 100%;
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.flame-link {
    color: var(--accent-gold);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.flame-link:hover {
    color: var(--primary-light);
    text-shadow: 0 0 10px var(--flame-glow);
}

.divider {
    color: rgba(255, 69, 0, 0.6);
    margin: 0 15px;
    font-weight: bold;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .flame-card {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }

    .verification-group {
        flex-direction: column;
        gap: 15px;
    }

    .verify-btn, .flame-btn-small {
        width: 100%;
    }

    .flame-icon {
        font-size: 2.5rem;
    }
}
