* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', 'Times New Roman', serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 25%, #fd79a8 50%, #a29bfe 75%, #6c5ce7 100%);
    color: #2d3436;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(253, 121, 168, 0.3);
    border-radius: 25px;
    color: #fd79a8;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(253, 121, 168, 0.2);
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(253, 121, 168, 0.3);
}

/* 水彩背景 */
.watercolor-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.watercolor-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: watercolorFloat 8s ease-in-out infinite;
    opacity: 0.6;
}

.blob-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 107, 107, 0.6) 0%, transparent 70%);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.blob-2 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(116, 185, 255, 0.6) 0%, transparent 70%);
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.blob-3 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(162, 155, 254, 0.6) 0%, transparent 70%);
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.blob-4 {
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(255, 234, 167, 0.6) 0%, transparent 70%);
    top: 30%;
    left: 50%;
    animation-delay: 6s;
}

.watercolor-splash {
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(253, 121, 168, 0.4) 0%, transparent 60%);
    border-radius: 50%;
    filter: blur(20px);
    animation: splashMove 6s ease-in-out infinite;
}

.splash-1 {
    top: 20%;
    right: 30%;
    animation-delay: 1s;
}

.splash-2 {
    bottom: 30%;
    left: 40%;
    animation-delay: 3s;
}

.paint-drops {
    position: absolute;
    width: 100%;
    height: 100%;
}

.drop {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(253, 121, 168, 0.7);
    border-radius: 50%;
    animation: dropFall 4s ease-in-out infinite;
}

.drop:nth-child(1) { left: 20%; animation-delay: 0s; }
.drop:nth-child(2) { left: 60%; animation-delay: 1.5s; }
.drop:nth-child(3) { left: 80%; animation-delay: 3s; }

@keyframes watercolorFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-30px) scale(1.1); }
}

@keyframes splashMove {
    0%, 100% { transform: translateX(0px) rotate(0deg); }
    50% { transform: translateX(20px) rotate(180deg); }
}

@keyframes dropFall {
    0% { top: -10px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { top: 100vh; opacity: 0; }
}

/* 表单容器 */
.form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    transition: all 0.5s ease;
    opacity: 1;
    visibility: visible;
}

.form-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.watercolor-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border: 3px solid transparent;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 
        0 8px 32px rgba(253, 121, 168, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.watercolor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(116, 185, 255, 0.1), rgba(162, 155, 254, 0.1));
    border-radius: 20px;
    z-index: -1;
}

.watercolor-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 12px 40px rgba(253, 121, 168, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.watercolor-card h2 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: 300;
    color: #fd79a8;
    letter-spacing: 1px;
    text-shadow: 2px 2px 4px rgba(253, 121, 168, 0.2);
}

.subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: #a29bfe;
    font-size: 14px;
    font-weight: 400;
    font-style: italic;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-group input {
    width: 100%;
    padding: 16px 12px 16px 12px;
    border: none;
    background: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    color: #2d3436;
    outline: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-family: 'Georgia', serif;
}

.form-group input:focus {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(253, 121, 168, 0.2);
}

.form-group label {
    position: absolute;
    top: 12px;
    left: 12px;
    color: #636e72;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    font-family: 'Georgia', serif;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: 0px;
    font-size: 12px;
    color: #fd79a8;
    font-weight: 500;
}

.watercolor-underline {
    position: absolute;
    bottom: 0;
    left: 12px;
    right: 12px;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #74b9ff, #a29bfe);
    border-radius: 2px;
    transform: scaleX(0);
    transition: all 0.3s ease;
}

.form-group input:focus ~ .watercolor-underline {
    transform: scaleX(1);
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 16px 20px;
    background: linear-gradient(45deg, #fd79a8, #a29bfe);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-family: 'Georgia', serif;
    box-shadow: 0 4px 15px rgba(253, 121, 168, 0.3);
}

.verify-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(253, 121, 168, 0.4);
}

/* 水彩按钮 */
.watercolor-btn {
    width: 100%;
    padding: 16px;
    margin: 32px 0 24px 0;
    background: linear-gradient(45deg, #fd79a8, #a29bfe, #74b9ff);
    background-size: 200% 200%;
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: 'Georgia', serif;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    animation: gradientShift 3s ease-in-out infinite;
}

.watercolor-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(253, 121, 168, 0.4);
}

.paint-splash {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.watercolor-btn:active .paint-splash {
    width: 200px;
    height: 200px;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 附加链接 */
.additional-links {
    text-align: center;
    font-size: 14px;
}

.additional-links a {
    color: #fd79a8;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.additional-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #fd79a8, #a29bfe);
    transition: all 0.3s ease;
}

.additional-links a:hover::after {
    width: 100%;
}

.additional-links a:hover {
    color: #a29bfe;
}

.divider {
    margin: 0 12px;
    color: #b2bec3;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .form-container {
        padding: 16px;
    }
    
    .watercolor-card {
        padding: 24px;
    }
    
    .watercolor-card h2 {
        font-size: 24px;
    }
    
    .back-button {
        top: 16px;
        left: 16px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .watercolor-blob {
        display: none;
    }
}

/* 加载动画 */
@keyframes watercolorFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
        filter: blur(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

.watercolor-card {
    animation: watercolorFadeIn 1s ease-out;
}
