* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', 'Times New Roman', serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 25%, #ffd3a5 50%, #fd9853 75%, #ff8a80 100%);
    color: #2e7d32;
    overflow: hidden;
}

.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(76, 175, 80, 0.9);
    border: 2px solid #4caf50;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.back-button:hover {
    background: rgba(56, 142, 60, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.nature-background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.organic-shape {
    position: absolute;
    border-radius: 50% 30% 70% 40%;
    background: rgba(76, 175, 80, 0.1);
    animation: organicFloat 8s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 150px;
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: 3s;
}

.shape-3 {
    width: 120px;
    height: 120px;
    bottom: 20%;
    left: 20%;
    animation-delay: 6s;
}

.leaf-decoration, .flower-decoration {
    position: absolute;
    font-size: 24px;
    animation: leafFloat 6s ease-in-out infinite;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.leaf-1 { top: 20%; left: 15%; animation-delay: 0s; }
.leaf-2 { top: 70%; right: 20%; animation-delay: 2s; }
.leaf-3 { bottom: 30%; left: 25%; animation-delay: 4s; }
.flower-1 { top: 40%; right: 30%; animation-delay: 1s; }
.flower-2 { bottom: 40%; right: 15%; animation-delay: 5s; }

.nature-particles .particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #4caf50;
    border-radius: 50%;
    animation: particleFloat 8s ease-in-out infinite;
    opacity: 0.7;
}

.particle:nth-child(1) { top: 25%; left: 30%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 70%; animation-delay: 2s; }
.particle:nth-child(3) { top: 80%; left: 40%; animation-delay: 4s; }
.particle:nth-child(4) { top: 40%; left: 80%; animation-delay: 6s; }

@keyframes organicFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
    50% { transform: translateY(-20px) rotate(5deg) scale(1.05); }
}

@keyframes leafFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(10deg); }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
    50% { transform: translateY(-25px) scale(1.2); opacity: 1; }
}

.form-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    width: 100%;
    max-width: 400px;
    padding: 20px;
    transition: all 0.5s ease;
    opacity: 1;
    visibility: visible;
}

.form-container.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.nature-card {
    background: rgba(255, 255, 255, 0.95);
    border: 3px solid #4caf50;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, transparent 50%, rgba(139, 195, 74, 0.05) 100%);
    border-radius: 25px;
    pointer-events: none;
}

.nature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(76, 175, 80, 0.3);
}

.nature-card h2 {
    text-align: center;
    margin-bottom: 8px;
    font-size: 28px;
    font-weight: 300;
    color: #2e7d32;
    letter-spacing: 1px;
}

.subtitle {
    text-align: center;
    margin-bottom: 32px;
    color: #4caf50;
    font-size: 14px;
    font-weight: 400;
    font-style: italic;
}

.form-group {
    position: relative;
    margin-bottom: 24px;
}

.form-group input {
    width: 100%;
    padding: 16px 12px 8px 12px;
    border: none;
    background: rgba(76, 175, 80, 0.05);
    font-size: 16px;
    color: #2e7d32;
    outline: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    font-family: 'Georgia', serif;
    border: 2px solid rgba(76, 175, 80, 0.2);
}

.form-group input:focus {
    background: rgba(76, 175, 80, 0.1);
    border-color: #4caf50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.2);
}

.form-group label {
    position: absolute;
    top: 16px;
    left: 12px;
    color: #4caf50;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    font-family: 'Georgia', serif;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -8px;
    left: 8px;
    font-size: 12px;
    color: #2e7d32;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 4px;
    border-radius: 4px;
    font-weight: 500;
}

.organic-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid transparent;
    border-radius: 15px;
    background: linear-gradient(45deg, #4caf50, #8bc34a, #cddc39) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.form-group input:focus ~ .organic-border {
    opacity: 1;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 16px 20px;
    background: linear-gradient(45deg, #8bc34a, #4caf50);
    border: none;
    border-radius: 15px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-family: 'Georgia', serif;
    box-shadow: 0 4px 15px rgba(139, 195, 74, 0.3);
}

.verify-btn:hover {
    background: linear-gradient(45deg, #4caf50, #388e3c);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 195, 74, 0.4);
}

.nature-btn {
    width: 100%;
    padding: 16px;
    margin: 32px 0 24px 0;
    background: linear-gradient(45deg, #4caf50, #8bc34a);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: 'Georgia', serif;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

.nature-btn:hover {
    background: linear-gradient(45deg, #388e3c, #4caf50);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

.nature-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: all 0.5s ease;
}

.nature-btn:hover .nature-glow {
    left: 100%;
}

.additional-links {
    text-align: center;
    font-size: 14px;
}

.additional-links a {
    color: #4caf50;
    text-decoration: none;
    transition: all 0.3s ease;
}

.additional-links a:hover {
    color: #2e7d32;
}

.divider {
    margin: 0 12px;
    color: #8bc34a;
}

@media (max-width: 480px) {
    .form-container {
        padding: 16px;
    }
    
    .nature-card {
        padding: 24px;
    }
    
    .nature-card h2 {
        font-size: 24px;
    }
    
    .back-button {
        top: 16px;
        left: 16px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .organic-shape, .leaf-decoration, .flower-decoration {
        display: none;
    }
}

@keyframes natureFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.nature-card {
    animation: natureFadeIn 1s ease-out;
}
