// 区块链数字风格特定的JavaScript功能
document.addEventListener('DOMContentLoaded', () => {
    // 初始化区块链效果
    initBlockchainEffects();
    
    // 初始化网络画布
    initBlockchainNetwork();
    
    // 初始化数字粒子效果
    initDigitalParticles();
    
    // 初始化哈希验证动画
    initHashValidation();
    
    // 初始化表单交互效果
    initFormInteractions();
    
    // 初始化钱包地址生成
    initWalletAddressGeneration();
    
    // 初始化页面动画
    initPageAnimations();
});

// 区块链网络可视化
function initBlockchainNetwork() {
    const canvas = document.getElementById('blockchainNetwork');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    const nodes = [];
    const connections = [];
    const nodeCount = 30;
    
    // 创建网络节点
    for (let i = 0; i < nodeCount; i++) {
        nodes.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            radius: Math.random() * 3 + 2,
            opacity: Math.random() * 0.8 + 0.2
        });
    }
    
    // 创建连接
    for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
            const dx = nodes[i].x - nodes[j].x;
            const dy = nodes[i].y - nodes[j].y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 150) {
                connections.push({
                    from: i,
                    to: j,
                    opacity: 1 - (distance / 150)
                });
            }
        }
    }
    
    // 动画循环
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 更新节点位置
        nodes.forEach(node => {
            node.x += node.vx;
            node.y += node.vy;
            
            // 边界反弹
            if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
            if (node.y < 0 || node.y > canvas.height) node.vy *= -1;
            
            // 绘制节点
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(0, 255, 136, ${node.opacity})`;
            ctx.fill();
            
            // 节点发光效果
            ctx.shadowBlur = 20;
            ctx.shadowColor = '#00ff88';
            ctx.fill();
            ctx.shadowBlur = 0;
        });
        
        // 绘制连接
        connections.forEach(connection => {
            const fromNode = nodes[connection.from];
            const toNode = nodes[connection.to];
            
            ctx.beginPath();
            ctx.moveTo(fromNode.x, fromNode.y);
            ctx.lineTo(toNode.x, toNode.y);
            ctx.strokeStyle = `rgba(0, 255, 136, ${connection.opacity * 0.5})`;
            ctx.lineWidth = 1;
            ctx.stroke();
        });
        
        // 重新计算连接
        connections.length = 0;
        for (let i = 0; i < nodes.length; i++) {
            for (let j = i + 1; j < nodes.length; j++) {
                const dx = nodes[i].x - nodes[j].x;
                const dy = nodes[i].y - nodes[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 150) {
                    connections.push({
                        from: i,
                        to: j,
                        opacity: 1 - (distance / 150)
                    });
                }
            }
        }
        
        requestAnimationFrame(animate);
    }
    
    animate();
    
    // 窗口大小调整
    window.addEventListener('resize', () => {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    });
}

// 数字粒子效果
function initDigitalParticles() {
    const container = document.getElementById('digitalParticles');
    if (!container) return;
    
    const particleCount = 50;
    const particles = [];
    
    // 创建粒子
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
        container.appendChild(particle);
        particles.push(particle);
    }
}

// 哈希验证动画
function initHashValidation() {
    const hashElements = document.querySelectorAll('.hash-hash');
    const statusElements = document.querySelectorAll('.hash-status');
    
    // 模拟哈希更新
    setInterval(() => {
        hashElements.forEach((hashEl, index) => {
            if (hashEl.textContent === 'generating...') {
                hashEl.textContent = generateHash();
                if (statusElements[index]) {
                    statusElements[index].textContent = '✓ 生成完成';
                    statusElements[index].classList.add('active');
                }
            } else if (hashEl.textContent !== 'recovery mode') {
                const currentHash = hashEl.textContent;
                const newHash = generateHash();
                if (currentHash !== newHash) {
                    hashEl.textContent = newHash;
                }
            }
        });
    }, 3000);
    
    // 生成模拟哈希
    function generateHash() {
        const chars = '0123456789abcdef';
        let hash = '0x';
        for (let i = 0; i < 8; i++) {
            hash += chars[Math.floor(Math.random() * chars.length)];
        }
        return hash + '...';
    }
}

// 表单交互效果
function initFormInteractions() {
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"], input[type="tel"]');
    
    inputs.forEach(input => {
        // 输入时添加发光效果
        input.addEventListener('input', () => {
            input.style.boxShadow = `0 0 ${input.value.length * 2}px rgba(0, 255, 136, 0.3)`;
        });
        
        // 失去焦点时重置
        input.addEventListener('blur', () => {
            input.style.boxShadow = '';
        });
        
        // 钱包地址验证
        if (input.id === 'username') {
            input.addEventListener('input', () => {
                const value = input.value;
                if (value.startsWith('0x') && value.length >= 10) {
                    input.style.borderColor = '#00ff88';
                    input.style.boxShadow = '0 0 20px rgba(0, 255, 136, 0.5)';
                } else if (value.length > 0) {
                    input.style.borderColor = '#ff4444';
                    input.style.boxShadow = '0 0 20px rgba(255, 68, 68, 0.5)';
                }
            });
        }
        
        // 密码强度检测
        if (input.type === 'password') {
            input.addEventListener('input', () => {
                const strength = calculatePasswordStrength(input.value);
                updateSecurityIndicator(strength);
            });
        }
    });
    
    // 验证码按钮交互
    const verifyButtons = document.querySelectorAll('.verify-btn');
    verifyButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            startCodeCountdown(btn);
        });
    });
    
    // 二维码动画
    const qrCode = document.getElementById('qrCode');
    if (qrCode) {
        qrCode.addEventListener('click', () => {
            qrCode.style.animation = 'none';
            setTimeout(() => {
                qrCode.style.animation = 'qr-scan 2s ease-in-out infinite';
            }, 100);
        });
    }
}

// 密码强度计算
function calculatePasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength += 25;
    if (password.length >= 12) strength += 25;
    if (/[a-z]/.test(password)) strength += 15;
    if (/[A-Z]/.test(password)) strength += 15;
    if (/[0-9]/.test(password)) strength += 10;
    if (/[^a-zA-Z0-9]/.test(password)) strength += 10;
    
    return Math.min(strength, 100);
}

// 更新安全指示器
function updateSecurityIndicator(strength) {
    const indicator = document.querySelector('.security-indicator');
    if (!indicator) return;
    
    const securityBar = indicator.querySelector('.security-bar');
    const securityText = indicator.querySelector('.security-text');
    
    if (strength >= 80) {
        indicator.className = 'security-indicator high';
        securityBar.style.background = 'linear-gradient(90deg, #00ff88, #00ccff)';
        securityText.textContent = '高安全性';
    } else if (strength >= 50) {
        indicator.className = 'security-indicator medium';
        securityBar.style.background = 'linear-gradient(90deg, #ffaa00, #ff6600)';
        securityText.textContent = '中等安全性';
    } else {
        indicator.className = 'security-indicator low';
        securityBar.style.background = 'linear-gradient(90deg, #ff4444, #cc0000)';
        securityText.textContent = '低安全性';
    }
    
    securityBar.style.width = strength + '%';
}

// 验证码倒计时
function startCodeCountdown(button) {
    let count = 60;
    button.disabled = true;
    button.textContent = count + 's';
    
    const countdown = setInterval(() => {
        count--;
        if (count <= 0) {
            clearInterval(countdown);
            button.disabled = false;
            button.textContent = '获取验证';
        } else {
            button.textContent = count + 's';
        }
    }, 1000);
}

// 钱包地址生成
function initWalletAddressGeneration() {
    const usernameInput = document.getElementById('regUsername');
    const addressPreview = document.getElementById('walletAddressPreview');
    
    if (usernameInput && addressPreview) {
        usernameInput.addEventListener('input', () => {
            const username = usernameInput.value;
            if (username.length >= 2) {
                addressPreview.textContent = generateWalletAddress(username);
            } else {
                addressPreview.textContent = '0x....生成中';
            }
        });
    }
}

// 生成模拟钱包地址
function generateWalletAddress(username) {
    const chars = '0123456789abcdef';
    let address = '0x';
    
    // 基于用户名生成地址
    const seed = username.charCodeAt(0) + username.length;
    for (let i = 0; i < 40; i++) {
        const charIndex = (seed + i) % chars.length;
        address += chars[charIndex];
    }
    
    return address;
}

// 区块链特效
function initBlockchainEffects() {
    // 数据区块动画更新
    const dataBlocks = document.querySelectorAll('.data-block');
    dataBlocks.forEach((block, index) => {
        setInterval(() => {
            const blockNumber = Math.floor(Math.random() * 999) + 1;
            block.textContent = `Block #${blockNumber.toString().padStart(3, '0')}`;
        }, 5000 + index * 1000);
    });
    
    // 加密货币符号位置更新
    const cryptoSymbols = document.querySelectorAll('.crypto-symbol');
    cryptoSymbols.forEach((symbol, index) => {
        // 随机位置动画
        setInterval(() => {
            const randomX = Math.random() * (window.innerWidth - 100);
            const randomY = Math.random() * (window.innerHeight - 100);
            
            symbol.style.transition = 'all 3s ease-in-out';
            symbol.style.left = randomX + 'px';
            symbol.style.top = randomY + 'px';
        }, 8000 + index * 2000);
    });
}

// 页面动画
function initPageAnimations() {
    // 卡片入场动画
    const cryptoCard = document.querySelector('.crypto-card');
    if (cryptoCard) {
        cryptoCard.style.opacity = '0';
        cryptoCard.style.transform = 'translateY(50px) scale(0.9)';
        
        setTimeout(() => {
            cryptoCard.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            cryptoCard.style.opacity = '1';
            cryptoCard.style.transform = 'translateY(0) scale(1)';
        }, 200);
    }
    
    // 返回按钮动画
    const backButton = document.querySelector('.back-button');
    if (backButton) {
        backButton.style.opacity = '0';
        backButton.style.transform = 'translateX(-30px)';
        
        setTimeout(() => {
            backButton.style.transition = 'all 0.6s ease-out';
            backButton.style.opacity = '1';
            backButton.style.transform = 'translateX(0)';
        }, 500);
    }
    
    // 表单组延迟动画
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach((group, index) => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            group.style.transition = 'all 0.6s ease-out';
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, 800 + index * 150);
    });
    
    // 登录按钮动画
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.style.opacity = '0';
        loginBtn.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            loginBtn.style.transition = 'all 0.8s ease-out';
            loginBtn.style.opacity = '1';
            loginBtn.style.transform = 'translateY(0)';
        }, 1500);
    }
    
    // 装饰元素动画
    const decorationElements = document.querySelectorAll('.crypto-symbol, .data-block');
    decorationElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'scale(0)';
        
        setTimeout(() => {
            element.style.transition = 'all 1s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
        }, 200 + index * 100);
    });
}

// 添加键盘导航支持
document.addEventListener('keydown', (e) => {
    // Enter键提交表单
    if (e.key === 'Enter') {
        const activeForm = document.querySelector('.form-container:not(.hidden) form');
        if (activeForm) {
            e.preventDefault();
            activeForm.reportValidity();
            // 这里可以添加表单提交逻辑
        }
    }
    
    // Tab键导航增强
    if (e.key === 'Tab') {
        const focusableElements = document.querySelectorAll('input, button, a');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
});

// 添加触摸设备支持
if ('ontouchstart' in window) {
    document.querySelectorAll('input, button, a').forEach(element => {
        element.addEventListener('touchstart', () => {
            element.style.transform = 'scale(0.98)';
        });
        
        element.addEventListener('touchend', () => {
            element.style.transform = '';
        });
    });
}

// 添加密码显示/隐藏功能
document.querySelectorAll('input[type="password"]').forEach(passwordInput => {
    // 创建显示/隐藏按钮
    const toggleButton = document.createElement('button');
    toggleButton.type = 'button';
    toggleButton.className = 'password-toggle';
    toggleButton.textContent = '👁️';
    toggleButton.style.cssText = `
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 16px;
        opacity: 0.7;
    `;
    
    passwordInput.parentElement.style.position = 'relative';
    passwordInput.parentElement.appendChild(toggleButton);
    
    toggleButton.addEventListener('click', () => {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleButton.textContent = '🙈';
        } else {
            passwordInput.type = 'password';
            toggleButton.textContent = '👁️';
        }
    });
});