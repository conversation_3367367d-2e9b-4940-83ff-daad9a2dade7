<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 樱花和风风格登录</title>
    <link rel="stylesheet" href="../assets/styles/sakura-zen.css">
</head>
<body>
    <!-- 返回按钮 -->
    <div class="back-button" onclick="window.location.href='/index.html'">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回选择
    </div>

    <!-- 樱花和风背景 -->
    <div class="sakura-background">
        <!-- 水墨画背景 -->
        <div class="ink-wash-layer">
            <div class="ink-cloud ink-cloud1"></div>
            <div class="ink-cloud ink-cloud2"></div>
            <div class="ink-cloud ink-cloud3"></div>
        </div>
        
        <!-- 樱花花瓣容器 -->
        <div class="sakura-petals-container">
            <div class="sakura-petal">🌸</div>
            <div class="sakura-petal">🌺</div>
            <div class="sakura-petal">🌸</div>
            <div class="sakura-petal">🌺</div>
            <div class="sakura-petal">🌸</div>
            <div class="sakura-petal">🌺</div>
            <div class="sakura-petal">🌸</div>
            <div class="sakura-petal">🌺</div>
        </div>
        
        <!-- 竹林装饰 -->
        <div class="bamboo-decorations">
            <div class="bamboo-stalk bamboo-stalk1">🎋</div>
            <div class="bamboo-stalk bamboo-stalk2">🎋</div>
            <div class="bamboo-leaf">🍃</div>
            <div class="bamboo-leaf">🍃</div>
        </div>
        
        <!-- 纸灯笼 -->
        <div class="paper-lanterns">
            <div class="paper-lantern lantern1">🏮</div>
            <div class="paper-lantern lantern2">🏮</div>
        </div>
        
        <!-- 蝴蝶飞舞 -->
        <div class="butterflies">
            <div class="butterfly">🦋</div>
            <div class="butterfly">🦋</div>
            <div class="butterfly">🦋</div>
        </div>
    </div>
    
    <!-- 登录表单 -->
    <div class="login-container form-container" id="loginForm">
        <div class="zen-card">
            <div class="card-header">
                <div class="zen-icon">🌸</div>
                <h2>和风登录</h2>
                <p class="subtitle">SAKURA ZEN STYLE</p>
                <div class="zen-divider"></div>
            </div>
            <form class="login-form">
                <div class="form-group">
                    <input type="text" id="username" required>
                    <label for="username">用户名</label>
                    <div class="ink-line"></div>
                </div>
                
                <div class="form-group">
                    <input type="password" id="password" required>
                    <label for="password">密码</label>
                    <div class="ink-line"></div>
                </div>
                
                <button type="submit" class="login-btn zen-btn">
                    <span class="btn-text">登录</span>
                    <div class="btn-ink"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="toForgotPassword" class="zen-link">忘记密码？</a>
                    <span class="divider">🌸</span>
                    <a href="#" id="toRegister" class="zen-link">注册新用户</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 注册表单 -->
    <div class="login-container form-container hidden" id="registerForm">
        <div class="zen-card">
            <div class="card-header">
                <div class="zen-icon">🌺</div>
                <h2>用户注册</h2>
                <p class="subtitle">JOIN THE ZEN</p>
                <div class="zen-divider"></div>
            </div>
            <form>
                <div class="form-group">
                    <input type="text" id="regUsername" required>
                    <label for="regUsername">用户名</label>
                    <div class="ink-line"></div>
                </div>
                <div class="form-group">
                    <input type="tel" id="regPhone" pattern="[0-9]{11}" maxlength="11" required>
                    <label for="regPhone">手机号码</label>
                    <div class="ink-line"></div>
                </div>
                <div class="form-group verification-group">
                    <input type="text" id="regVerificationCode" pattern="[0-9]{6}" maxlength="6" required>
                    <label for="regVerificationCode">验证码</label>
                    <div class="ink-line"></div>
                    <button type="button" class="verify-btn zen-btn-small" id="regSendCodeBtn">获取验证码</button>
                </div>
                <div class="form-group">
                    <input type="password" id="regPassword" required>
                    <label for="regPassword">密码</label>
                    <div class="ink-line"></div>
                </div>
                <div class="form-group">
                    <input type="password" id="regConfirmPassword" required>
                    <label for="regConfirmPassword">确认密码</label>
                    <div class="ink-line"></div>
                </div>
                <button type="submit" class="login-btn zen-btn">
                    <span class="btn-text">注册</span>
                    <div class="btn-ink"></div>
                </button>
                <div class="additional-links">
                    <a href="#" id="backToLogin" class="zen-link">返回登录</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 忘记密码表单 -->
    <div class="login-container form-container hidden" id="forgotPasswordForm">
        <div class="zen-card">
            <div class="card-header">
                <div class="zen-icon">🎋</div>
                <h2>重置密码</h2>
                <p class="subtitle">RESET PASSWORD</p>
                <div class="zen-divider"></div>
            </div>
            <form>
                <div class="form-group">
                    <input type="tel" id="resetPhone" pattern="[0-9]{11}" maxlength="11" required>
                    <label for="resetPhone">手机号码</label>
                    <div class="ink-line"></div>
                </div>
                <div class="form-group verification-group">
                    <input type="text" id="verificationCode" pattern="[0-9]{6}" maxlength="6" required>
                    <label for="verificationCode">验证码</label>
                    <div class="ink-line"></div>
                    <button type="button" class="verify-btn zen-btn-small" id="sendCodeBtn">获取验证码</button>
                </div>
                <div class="form-group">
                    <input type="password" id="newPassword" required>
                    <label for="newPassword">新密码</label>
                    <div class="ink-line"></div>
                </div>
                <div class="form-group">
                    <input type="password" id="confirmNewPassword" required>
                    <label for="confirmNewPassword">确认新密码</label>
                    <div class="ink-line"></div>
                </div>
                <button type="submit" class="login-btn zen-btn">
                    <span class="btn-text">重置密码</span>
                    <div class="btn-ink"></div>
                </button>
                <div class="additional-links">
                    <a href="#" id="backToLoginFromReset" class="zen-link">返回登录</a>
                </div>
            </form>
        </div>
    </div>

    <script src="../js/common.js"></script>
    <script src="../js/sakura-zen.js"></script>
</body>
</html>
