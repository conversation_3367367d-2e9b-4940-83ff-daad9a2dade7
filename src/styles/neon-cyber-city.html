<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌃 霓虹赛博城市风格登录</title>
    <link rel="stylesheet" href="../assets/styles/neon-cyber-city.css">
</head>
<body>
    <!-- 返回按钮 -->
    <div class="back-button" onclick="window.location.href='/index.html'">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回选择
    </div>

    <!-- 赛博城市背景 -->
    <div class="cyber-city-background">
        <div class="city-skyline">
            <div class="building building-1"></div>
            <div class="building building-2"></div>
            <div class="building building-3"></div>
            <div class="building building-4"></div>
            <div class="building building-5"></div>
            <div class="building building-6"></div>
        </div>
        <div class="neon-signs">
            <div class="neon-sign sign-1">CYBER</div>
            <div class="neon-sign sign-2">CITY</div>
            <div class="neon-sign sign-3">2077</div>
        </div>
        <div class="flying-cars">
            <div class="flying-car car-1">🚗</div>
            <div class="flying-car car-2">🚙</div>
            <div class="flying-car car-3">🚐</div>
        </div>
        <div class="hologram-ads">
            <div class="hologram ad-1">📱</div>
            <div class="hologram ad-2">💻</div>
            <div class="hologram ad-3">🎮</div>
        </div>
        <div class="rain-effect">
            <div class="rain-drop drop-1"></div>
            <div class="rain-drop drop-2"></div>
            <div class="rain-drop drop-3"></div>
            <div class="rain-drop drop-4"></div>
            <div class="rain-drop drop-5"></div>
        </div>
    </div>
    
    <!-- 登录表单 -->
    <div class="login-container form-container" id="loginForm">
        <div class="cyber-card">
            <div class="card-header">
                <div class="status-led"></div>
                <h2>🌃 城市登录</h2>
                <div class="signal-bars">
                    <div class="bar bar-1"></div>
                    <div class="bar bar-2"></div>
                    <div class="bar bar-3"></div>
                    <div class="bar bar-4"></div>
                </div>
            </div>
            <p class="subtitle">霓虹赛博城市风格</p>
            <form class="login-form">
                <div class="form-group">
                    <input type="text" id="username" required>
                    <label for="username">用户名</label>
                    <div class="neon-underline"></div>
                </div>
                
                <div class="form-group">
                    <input type="password" id="password" required>
                    <label for="password">密码</label>
                    <div class="neon-underline"></div>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-text">接入城市网络</span>
                    <div class="btn-glow"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="toForgotPassword" class="cyber-link">
                        <span>🔐</span> 密码恢复
                    </a>
                    <span class="divider">|</span>
                    <a href="#" id="toRegister" class="cyber-link">
                        <span>📝</span> 注册账户
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 注册表单 -->
    <div class="login-container form-container hidden" id="registerForm">
        <div class="cyber-card">
            <div class="card-header">
                <div class="status-led register-mode"></div>
                <h2>🌃 用户注册</h2>
                <div class="signal-bars">
                    <div class="bar bar-1"></div>
                    <div class="bar bar-2"></div>
                    <div class="bar bar-3"></div>
                    <div class="bar bar-4"></div>
                </div>
            </div>
            <p class="subtitle">创建城市网络账户</p>
            <form>
                <div class="form-group">
                    <input type="text" id="regUsername" required>
                    <label for="regUsername">用户名</label>
                    <div class="neon-underline"></div>
                </div>
                <div class="form-group">
                    <input type="tel" id="regPhone" pattern="[0-9]{11}" maxlength="11" required>
                    <label for="regPhone">手机号码</label>
                    <div class="neon-underline"></div>
                </div>
                <div class="form-group verification-group">
                    <input type="text" id="regVerificationCode" pattern="[0-9]{6}" maxlength="6" required>
                    <label for="regVerificationCode">验证码</label>
                    <div class="neon-underline"></div>
                    <button type="button" class="verify-btn" id="regSendCodeBtn">获取验证码</button>
                </div>
                <div class="form-group">
                    <input type="password" id="regPassword" required>
                    <label for="regPassword">密码</label>
                    <div class="neon-underline"></div>
                </div>
                <div class="form-group">
                    <input type="password" id="regConfirmPassword" required>
                    <label for="regConfirmPassword">确认密码</label>
                    <div class="neon-underline"></div>
                </div>
                
                <button type="submit" class="login-btn register-btn">
                    <span class="btn-text">创建账户</span>
                    <div class="btn-glow"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="backToLogin" class="cyber-link">
                        <span>🔙</span> 返回登录
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 忘记密码表单 -->
    <div class="login-container form-container hidden" id="forgotPasswordForm">
        <div class="cyber-card">
            <div class="card-header">
                <div class="status-led reset-mode"></div>
                <h2>🌃 密码恢复</h2>
                <div class="signal-bars">
                    <div class="bar bar-1"></div>
                    <div class="bar bar-2"></div>
                    <div class="bar bar-3"></div>
                    <div class="bar bar-4"></div>
                </div>
            </div>
            <p class="subtitle">恢复城市网络访问权限</p>
            <form>
                <div class="form-group">
                    <input type="tel" id="resetPhone" pattern="[0-9]{11}" maxlength="11" required>
                    <label for="resetPhone">手机号码</label>
                    <div class="neon-underline"></div>
                </div>
                <div class="form-group verification-group">
                    <input type="text" id="resetVerificationCode" pattern="[0-9]{6}" maxlength="6" required>
                    <label for="resetVerificationCode">验证码</label>
                    <div class="neon-underline"></div>
                    <button type="button" class="verify-btn" id="resetSendCodeBtn">获取验证码</button>
                </div>
                <div class="form-group">
                    <input type="password" id="newPassword" required>
                    <label for="newPassword">新密码</label>
                    <div class="neon-underline"></div>
                </div>
                <div class="form-group">
                    <input type="password" id="confirmNewPassword" required>
                    <label for="confirmNewPassword">确认新密码</label>
                    <div class="neon-underline"></div>
                </div>
                
                <button type="submit" class="login-btn reset-btn">
                    <span class="btn-text">重置密码</span>
                    <div class="btn-glow"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="backToLoginFromReset" class="cyber-link">
                        <span>🔙</span> 返回登录
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="../js/common.js"></script>
    <script src="../js/neon-cyber-city.js"></script>
</body>
</html>
