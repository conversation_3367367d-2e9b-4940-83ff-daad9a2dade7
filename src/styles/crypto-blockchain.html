<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⛓️ 区块链数字风格登录</title>
    <link rel="stylesheet" href="../assets/styles/crypto-blockchain.css">
</head>
<body>
    <!-- 返回按钮 -->
    <div class="back-button" onclick="window.location.href='/index.html'">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回选择
    </div>

    <!-- 区块链装饰元素 -->
    <div class="blockchain-decoration">
        <!-- 动态区块链网络 -->
        <canvas id="blockchainNetwork" class="blockchain-canvas"></canvas>
        
        <!-- 流动的加密货币符号 -->
        <div class="crypto-symbols">
            <div class="crypto-symbol btc">₿</div>
            <div class="crypto-symbol eth">Ξ</div>
            <div class="crypto-symbol ada">₳</div>
            <div class="crypto-symbol dot">●</div>
        </div>
        
        <!-- 数据区块动画 -->
        <div class="data-blocks">
            <div class="data-block block-1">Block #001</div>
            <div class="data-block block-2">Block #002</div>
            <div class="data-block block-3">Block #003</div>
            <div class="data-block block-4">Block #004</div>
        </div>
        
        <!-- 数字化粒子效果 -->
        <div class="digital-particles" id="digitalParticles"></div>
        
        <!-- 密码学图形装饰 -->
        <div class="cryptography-ornaments">
            <div class="crypto-key">🔑</div>
            <div class="crypto-lock">🔒</div>
            <div class="crypto-shield">🛡️</div>
        </div>
        
        <!-- 网格背景 -->
        <div class="crypto-grid"></div>
    </div>

    <!-- 登录表单 -->
    <div class="login-container form-container" id="loginForm">
        <div class="crypto-card">
            <!-- 区块链图标 -->
            <div class="blockchain-icon">
                <div class="cube">
                    <div class="face front">⛓️</div>
                    <div class="face back">⚡</div>
                    <div class="face top">🔐</div>
                </div>
            </div>
            
            <h2>⛓️ 区块链安全登录</h2>
            <p class="subtitle">Crypto Blockchain Security</p>
            
            <!-- 哈希验证指示器 -->
            <div class="hash-indicator">
                <div class="hash-hash">0x7a9b4c2f8e1d3...</div>
                <div class="hash-status active">✓ 安全连接</div>
            </div>
            
            <form class="login-form">
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">👤</span>
                        <label for="username">钱包地址</label>
                    </div>
                    <input type="text" id="username" placeholder="0x..." required>
                    <div class="input-decoration"></div>
                </div>
                
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">🔑</span>
                        <label for="password">私钥密码</label>
                    </div>
                    <input type="password" id="password" placeholder="输入安全密码" required>
                    <div class="input-decoration"></div>
                </div>
                
                <!-- 二维码扫描区域 -->
                <div class="qr-section">
                    <div class="qr-code-container">
                        <div class="qr-code" id="qrCode">
                            <div class="qr-placeholder">📱</div>
                        </div>
                        <p class="qr-text">或扫描二维码登录</p>
                    </div>
                </div>
                
                <!-- 安全等级显示 -->
                <div class="security-level">
                    <div class="security-indicator high">
                        <div class="security-bar"></div>
                        <span class="security-text">高安全性</span>
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-icon">⛓️</span>
                    <span class="btn-text">验证身份</span>
                    <div class="btn-effect"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="toForgotPassword">忘记私钥？</a>
                    <span class="divider">|</span>
                    <a href="#" id="toRegister">创建钱包</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 注册表单 -->
    <div class="login-container form-container hidden" id="registerForm">
        <div class="crypto-card">
            <div class="blockchain-icon">
                <div class="cube">
                    <div class="face front">🆕</div>
                    <div class="face back">⚡</div>
                    <div class="face top">🔐</div>
                </div>
            </div>
            
            <h2>⛓️ 创建数字钱包</h2>
            <p class="subtitle">Secure Blockchain Wallet</p>
            
            <div class="hash-indicator">
                <div class="hash-hash">generating...</div>
                <div class="hash-status">生成中...</div>
            </div>
            
            <form>
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">🏷️</span>
                        <label for="regUsername">钱包名称</label>
                    </div>
                    <input type="text" id="regUsername" placeholder="我的钱包" required>
                    <div class="input-decoration"></div>
                </div>
                
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">📱</span>
                        <label for="regPhone">手机验证</label>
                    </div>
                    <input type="tel" id="regPhone" pattern="[0-9]{11}" maxlength="11" placeholder="手机号码" required>
                    <div class="input-decoration"></div>
                </div>
                
                <div class="form-group verification-group">
                    <div class="input-label">
                        <span class="label-icon">🔢</span>
                        <label for="regVerificationCode">验证码</label>
                    </div>
                    <input type="text" id="regVerificationCode" pattern="[0-9]{6}" maxlength="6" placeholder="6位数字" required>
                    <div class="input-decoration"></div>
                    <button type="button" class="verify-btn" id="regSendCodeBtn">获取验证</button>
                </div>
                
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">🔐</span>
                        <label for="regPassword">安全密码</label>
                    </div>
                    <input type="password" id="regPassword" placeholder="强密码" required>
                    <div class="input-decoration"></div>
                </div>
                
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">🔐</span>
                        <label for="regConfirmPassword">确认密码</label>
                    </div>
                    <input type="password" id="regConfirmPassword" placeholder="再次输入" required>
                    <div class="input-decoration"></div>
                </div>
                
                <!-- 钱包地址预览 -->
                <div class="wallet-address-preview">
                    <div class="address-label">钱包地址预览:</div>
                    <div class="address-preview" id="walletAddressPreview">0x....生成中</div>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-icon">⛓️</span>
                    <span class="btn-text">创建钱包</span>
                    <div class="btn-effect"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="backToLogin">返回登录</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 忘记密码表单 -->
    <div class="login-container form-container hidden" id="forgotPasswordForm">
        <div class="crypto-card">
            <div class="blockchain-icon">
                <div class="cube">
                    <div class="face front">🔑</div>
                    <div class="face back">⚡</div>
                    <div class="face top">🔐</div>
                </div>
            </div>
            
            <h2>⛓️ 恢复钱包私钥</h2>
            <p class="subtitle">Private Key Recovery</p>
            
            <div class="hash-indicator">
                <div class="hash-hash">recovery mode</div>
                <div class="hash-status">恢复模式</div>
            </div>
            
            <form>
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">📱</span>
                        <label for="resetPhone">手机验证</label>
                    </div>
                    <input type="tel" id="resetPhone" pattern="[0-9]{11}" maxlength="11" placeholder="注册手机" required>
                    <div class="input-decoration"></div>
                </div>
                
                <div class="form-group verification-group">
                    <div class="input-label">
                        <span class="label-icon">🔢</span>
                        <label for="resetVerificationCode">验证码</label>
                    </div>
                    <input type="text" id="resetVerificationCode" pattern="[0-9]{6}" maxlength="6" placeholder="6位数字" required>
                    <div class="input-decoration"></div>
                    <button type="button" class="verify-btn" id="resetSendCodeBtn">获取验证</button>
                </div>
                
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">🔐</span>
                        <label for="newPassword">新安全密码</label>
                    </div>
                    <input type="password" id="newPassword" placeholder="新密码" required>
                    <div class="input-decoration"></div>
                </div>
                
                <div class="form-group">
                    <div class="input-label">
                        <span class="label-icon">🔐</span>
                        <label for="confirmNewPassword">确认新密码</label>
                    </div>
                    <input type="password" id="confirmNewPassword" placeholder="再次输入" required>
                    <div class="input-decoration"></div>
                </div>
                
                <!-- 助记词验证 -->
                <div class="mnemonic-verification">
                    <div class="mnemonic-label">备份助记词验证:</div>
                    <div class="mnemonic-input">
                        <input type="text" placeholder="请输入12位助记词" class="mnemonic-field">
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-icon">🔓</span>
                    <span class="btn-text">恢复钱包</span>
                    <div class="btn-effect"></div>
                </button>
                
                <div class="additional-links">
                    <a href="#" id="backToLoginFromReset">返回登录</a>
                </div>
            </form>
        </div>
    </div>

    <script src="../js/common.js"></script>
    <script src="../js/crypto-blockchain.js"></script>
</body>
</html>